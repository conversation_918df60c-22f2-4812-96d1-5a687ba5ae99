//=============== Client Tooltip ================//

.giz-client-tooltip {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 200;
    visibility: hidden;
    background-color: #22272B;
    color: #FFFFFF;
    padding: 0.8rem 1.2rem;
    border-radius: 0.8rem;
    @include font-s-theme-client($font-weight-light-theme-client);
    filter: drop-shadow(0px 4px 20px rgba(0, 0, 0, 0.8));
    //box-shadow: 0 0.8rem 3.2rem rgba(0, 0, 0, 0.08);

    &-root {
        display: inline-block;
    }

    &--open {
        visibility: visible;
    }

    &--top {
        .giz-client-tooltip-pin {
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -0.5rem;
            border-width: 0.5rem;
            border-style: solid;
            border-color: #22272B transparent transparent transparent;
        }
    }

    &--bottom {
        .giz-client-tooltip-pin {
            position: absolute;
            top: -1rem;
            left: 50%;
            margin-left: -0.5rem;
            border-width: 0.5rem;
            border-style: solid;
            border-color: transparent transparent #22272B transparent;
        }
    }

    &--left {
        .giz-client-tooltip-pin {
            position: absolute;
            //???
            margin-left: -0.5rem;
            border-width: 0.5rem;
            border-style: solid;
            border-color: transparent transparent transparent #22272B;
        }
    }

    &--right {
        .giz-client-tooltip-pin {
            position: absolute;
            top: 50%;
            left: -1rem;
            margin-top: -0.5rem;
            border-width: 0.5rem;
            border-style: solid;
            border-color: transparent #22272B transparent transparent;
        }
    }
}