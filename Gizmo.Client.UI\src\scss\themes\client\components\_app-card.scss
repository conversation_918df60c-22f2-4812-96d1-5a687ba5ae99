//=============== Application Card ================//

.giz-app-card {
    position: relative;
    background-color: #22272B;
    border-radius: 0.8rem;
    overflow: hidden;

    &:hover {
        background-color: #020203;

        .giz-app-card__action {
            display: flex;
        }

        .giz-app-card__content__image__hovered {
            background-color: rgba(0, 0, 0, 0.94);

            .giz-app-card__content--hovered {
                visibility: visible;
            }
        }
    }

    &__content {
        display: grid;
        grid-template-rows: min-content 1fr min-content;
        gap: 1.6rem;
        height: 100%;

        &__image {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            height: 33.3rem;
            background-color: #161A1D;

            & > img {
                /*width: 100%;
                height: 100%;
                object-fit: cover;*/
                border-radius: 0.9rem 0.9rem 0 0;
            }

            &__hovered {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 2;
                padding: 1.6rem 0 0 0;
                transition: background-color 0.2s;

                &__header {
                    display: flex;
                    justify-content: space-between;
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                }

                .giz-app-card__content--hovered {
                    padding: 0 0.4rem 0 0.4rem;
                    visibility: hidden;
                }
            }
        }

        &__details {
            padding: 0 1.6rem;
            width: 100%;
            overflow: hidden;
        }

        &__footer {
            display: flex;
            justify-content: space-between;
            padding: 0.0rem 1.6rem 1.6rem 1.6rem;
            width: 100%;
            overflow: hidden;

            &-category {
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 16px;
                color: rgba(255, 255, 255, 0.6);
                padding: 0.6rem 1.2rem;
                white-space: nowrap;
                max-width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                @include font-s-theme-client($font-weight-regular-theme-client);
            }
        }

        &--hovered {
            height: 100%;
            width: 100%;
            overflow: auto;

            &__header {
                display: flex;
                justify-content: space-between;
            }
        }
    }

    &__action {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        padding: 1.6rem;
        display: none;
        align-items: center;
    }

    &-text {
        overflow: hidden;
    }

    &__title {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        @include font-l-theme-client($font-weight-light-theme-client);
    }
}

.giz-app-card-b1 {
    display: flex;
    align-items: center;
    background-color: #1B1D21;
    opacity: 0.9;
    border-radius: 1.2rem;
    padding: 0.4rem 0.8rem;
    color: #FAFAFA;
    @include font-s-theme-client($font-weight-light-theme-client);

    & > svg {
        margin-right: 0.4rem;
    }
}

.giz-app-card-b2 {
    display: flex;
    align-items: center;
    background-color: #1B1D21;
    opacity: 0.9;
    border-radius: 1.2rem;
    padding: 0.4rem 0.8rem;
    color: #FAFAFA;
    @include font-s-theme-client($font-weight-light-theme-client);

    & > svg {
        margin-right: 0.4rem;
    }
}

.giz-exe-popup {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 100%;
    min-height: 100%;
    overflow: hidden;
}
