﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase

<div class="giz-ads-carousel @(ViewState.Advertisements.Count() < 3 ? "giz-ads-carousel--simple" : "")">
    <CascadingValue Value="@this" IsFixed="true">
        <div class="giz-ads-carousel__body"
             @onmouseover="OnMouseOverHandler"
             @onmouseout="OnMouseOutHandler">

            @foreach (var item in ViewState.Advertisements)
            {
                <AdsCarouselItem @key="@item.Id" AdvertisementId="@item.Id" SimpleMode="@(ViewState.Advertisements.Count() < 3)" />
            }

        </div>
        @*@if (ViewState.Advertisements.Count() > 2)
        {
            <div class="giz-ads-carousel__navigation">
                <div class="giz-ads-carousel__navigation__indicators">
                    <AdsCarouselIndicator Maximum="5" Size="@(ViewState.Advertisements.Count())" SelectedIndex="@SelectedIndex" SelectedIndexChanged="SelectedIndexChangedHandler" />
                </div>
            </div>
        }*@
    </CascadingValue>
</div>