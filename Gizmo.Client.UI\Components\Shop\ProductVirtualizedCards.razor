﻿@namespace Gizmo.Client.UI.Components
@using Gizmo.Client.UI.View.States;
@using Microsoft.AspNetCore.Components.Web.Virtualization
@inherits CustomVirtualizedDOMComponentBase<UserProductViewState>

<Virtualize Items="Items.Chunk(ColumnsCount).ToArray()" Context="chunkedProducts">
    <div class="virtual-chunk-grid" style="@_gridColumnsStyle">
        @for (int i = 0; i < chunkedProducts.Length; i++)
        {
            <ProductCard @key="chunkedProducts[i].Id" Product="chunkedProducts[i]" />
        }
    </div>
</Virtualize>