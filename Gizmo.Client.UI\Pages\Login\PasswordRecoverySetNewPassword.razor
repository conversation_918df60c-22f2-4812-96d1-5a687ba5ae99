﻿@namespace Gizmo.Client.UI.Pages
@inherits CustomDOMComponentBase
@layout _Layout_Login
@using Gizmo.UI;
@using Microsoft.AspNetCore.Components.Forms

<LoginCard>
    <CardHeader>
        <div class="giz-login-links">
            @if (UserRegisterConfigurationViewState.IsEnabled)
            {
                <div class="giz-login-new-user">@LocalizationService.GetString("GIZ_LOGIN_NOT_A_MEMBER") <a @onclick="UserLoginService.OpenRegistrationAsync">@LocalizationService.GetString("GIZ_LOGIN_SIGN_UP_NOW")</a></div>
            }
        </div>
        <div class="giz-login-alerts">
            @if (ViewState.HasError)
            {
                <Toast CanClose="true" AlertType="AlertTypes.Danger" Text="@ViewState.ErrorMessage" />
            }
        </div>
    </CardHeader>
    <CardBody>
        <EditForm EditContext="@UserPasswordRecoverySetNewPasswordService.EditContext">
            <div class="giz-login-title">@LocalizationService.GetString("GIZ_PASSWORD_RECOVERY_FORGOT_PASSWORD")</div>
            <div class="giz-login-subtitle">@LocalizationService.GetString("GIZ_PASSWORD_RECOVERY_SET_PASSWORD_MESSAGE")</div>
            <div class="giz-form-input">
                <div class="giz-password-tooltip-root">
                    <PasswordInput ShowRevealButton="true"
                                   UpdateOnInput="true"
                                   Value="@ViewState.NewPassword"
                                   ValueChanged="@((string value) => UserPasswordRecoverySetNewPasswordService.SetNewPassword(value))"
                                   ValueExpression="@(() => ViewState.NewPassword)"
                                   IsDisabled="@ViewState.IsLoading"
                                   Size="InputSizes.Medium"
                                   IsFullWidth="true"
                                   Label="@LocalizationService.GetString("GIZ_GEN_ENTER_NEW_PASSWORD")" />
                    <PasswordTooltip Password="@ViewState.NewPassword" />
                </div>
            </div>
            <div class="giz-form-input">
                <PasswordInput UpdateOnInput="true"
                               Value="@ViewState.RepeatPassword"
                               ValueChanged="@((string value) => UserPasswordRecoverySetNewPasswordService.SetRepeatPassword(value))"
                               ValueExpression="@(() => ViewState.RepeatPassword)"
                               IsDisabled="@ViewState.IsLoading"
                               Size="InputSizes.Medium"
                               IsFullWidth="true"
                               Label="@LocalizationService.GetString("GIZ_GEN_REPEAT_NEW_PASSWORD")" />
            </div>
            <div>
                <Button IsDisabled="@(ViewState.IsValid == false || ViewState.IsLoading)"
                        Color="ButtonColors.Accent"
                        Size="ButtonSizes.ExtraLarge"
                        IsFullWidth="true"
                        @onclick="@UserPasswordRecoverySetNewPasswordService.SubmitAsync">
                    <ChildContent>
                        @if (ViewState.IsLoading)
                        {
                            <Spinner />
                        }
                        else
                        {
                            <div>@LocalizationService.GetString("GIZ_PASSWORD_RECOVERY_RESET_PASSWORD")</div>
                        }
                    </ChildContent>
                </Button>
            </div>
        </EditForm>
    </CardBody>
    <CardFooter>
        <InputLanguageMenu />
    </CardFooter>
</LoginCard>
