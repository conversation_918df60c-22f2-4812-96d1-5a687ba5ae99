﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase
@using Microsoft.AspNetCore.Components.Forms

<HostedDialog>
    <div class="giz-client-dialog giz-change-profile-dialog">
        <div class="giz-client-dialog__header">
            <div class="giz-client-dialog__header__title">@LocalizationService.GetString("GIZ_USER_BASIC_INFO")</div>
            @if (DisplayOptions.Closable)
            {
                <IconButton SVGIcon="Icons.Close" Variant="ButtonVariants.Text" Size="ButtonSizes.Small" Class="close-btn" @onclick="() => CloseDialog()" />
            }
        </div>

        @if (!ViewState.IsComplete)
        {
            <div class="giz-client-dialog__body">
                <EditForm EditContext="@UserChangeProfileViewStateService.EditContext">
                    <div class="giz-change-profile-section">
                        <div class="giz-change-profile-section__header">@LocalizationService.GetString("GIZ_USER_PROFILE_PICTURE_AND_USERNAME")</div>
                        <div class="giz-change-profile-section__body">
                            <div class="giz-change-profile-group">
                                <div class="giz-change-profile-group__username">
                                    <TextInput IsDisabled="true"
                                               IsTransparent="true"
                                               Value="@ViewState.Username"
                                               Size="InputSizes.Small"
                                               IsFullWidth="true"
                                               Label="@LocalizationService.GetString("GIZ_GEN_USERNAME")" />
                                    <div class="giz-change-profile-group__username__tip">Other users will see your username</div>
                                </div>
                                <div class="giz-change-profile-group__avatar">
                                    @if (ViewState.Picture != null)
                                    {
                                        <Avatar Image="@ViewState.Picture" Variant="AvatarVariants.Circle" Class="giz-user-avatar" />
                                    }
                                    else
                                    {
                                        <Icon SVGIcon="Icons.AccountCircle_Client" Class="giz-user-avatar" />
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="giz-change-profile-section">
                        <div class="giz-change-profile-section__header">@LocalizationService.GetString("GIZ_USER_PERSONAL_INFORMATION")</div>
                        <div class="giz-change-profile-section__body">
                            <div class="giz-change-profile-group">
                                <div>
                                    <TextInput UpdateOnInput="true"
                                               IsDisabled="@(!_isLoaded || !(ViewState.IsInitialized == true) || ViewState.IsLoading)"
                                               IsTransparent="true"
                                               Value="@ViewState.FirstName"
                                               ValueChanged="@((string value) => UserChangeProfileViewStateService.SetFirstName(value))"
                                               ValueExpression="@(() => ViewState.FirstName)"
                                               Size="InputSizes.Small"
                                               IsFullWidth="true"
                                               Label="@LocalizationService.GetString("GIZ_USER_FIRST_NAME")" />
                                </div>
                                <div>
                                    <TextInput UpdateOnInput="true"
                                               IsDisabled="@(!_isLoaded || !(ViewState.IsInitialized == true) || ViewState.IsLoading)"
                                               IsTransparent="true"
                                               Value="@ViewState.LastName"
                                               ValueChanged="@((string value) => UserChangeProfileViewStateService.SetLastName(value))"
                                               ValueExpression="@(() => ViewState.LastName)"
                                               Size="InputSizes.Small"
                                               IsFullWidth="true"
                                               Label="@LocalizationService.GetString("GIZ_USER_LAST_NAME")" />
                                </div>
                                <div>
                                    <MaskedDateInput IsDisabled="@(!_isLoaded || !(ViewState.IsInitialized == true) || ViewState.IsLoading)"
                                                     IsTransparent="true"
                                                     Value="@ViewState.BirthDate"
                                                     ValueChanged="@((DateTime? value) => UserChangeProfileViewStateService.SetBirthDate(value))"
                                                     ValueExpression="@(() => ViewState.BirthDate)"
                                                     Size="InputSizes.Small"
                                                     IsFullWidth="true"
                                                     Label="@LocalizationService.GetString("GIZ_USER_BIRTH_DATE")" />
                                </div>
                                <div>
                                    <Select IsDisabled="@(!_isLoaded || !(ViewState.IsInitialized == true) || ViewState.IsLoading)"
                                            IsTransparent="true"
                                            HandleSVGIcon="Icons.Select_Client"
                                            Value="@ViewState.Sex"
                                            ValueChanged="@((Sex value) => UserChangeProfileViewStateService.SetSex(value))"
                                            ValueExpression="@(() => ViewState.Sex)"
                                            Size="InputSizes.Small"
                                            IsFullWidth="true"
                                            Label="@LocalizationService.GetString("GIZ_USER_GENDER")"
                                            OpenDirection="PopupOpenDirections.Cursor"
                                            PopupClass="giz-scrollbar--v">
                                        <SelectItem Value="@Sex.Unspecified" Text="@LocalizationService.GetString("GIZ_GEN_SEX_UNSPECIFIED")" />
                                        <SelectItem Value="@Sex.Male" Text="@LocalizationService.GetString("GIZ_GEN_SEX_MALE")" />
                                        <SelectItem Value="@Sex.Female" Text="@LocalizationService.GetString("GIZ_GEN_SEX_FEMALE")" />
                                    </Select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="giz-change-profile-section">
                        <div class="giz-change-profile-section__header">@LocalizationService.GetString("GIZ_USER_LOCATION")</div>
                        <div class="giz-change-profile-section__body">
                            <IconSelect IsDisabled="@(!_isLoaded || !(ViewState.IsInitialized == true) || ViewState.IsLoading)"
                                        IsTransparent="true"
                                        Placeholder="@LocalizationService.GetString("GIZ_GEN_SEARCH")"
                                        IsVirtualized="false"
                                        IsLoading="@(!_isLoaded)"
                                        CanClearValue="@(!(GetSelectedCountry()?.PhonePrefix == "+"))"
                                        OnClickClearValueButton="OnClickClearValueButtonHandler"
                                        HandleSVGIcon="Icons.Select_Client"
                                        TValue="IconSelectCountry"
                                        ItemSource="@Countries"
                                        SelectedItem="@GetSelectedCountry()"
                                        SelectedItemChanged="@SetSelectedCountry"
                                        Size="InputSizes.Small"
                                        IsFullWidth="true"
                                        Label="@LocalizationService.GetString("GIZ_USER_COUNTRY")"
                                        OpenDirection="PopupOpenDirections.Cursor"
                                        MaximumHeight="20.0rem"
                                        PopupClass="giz-scrollbar--v" />
                        </div>
                    </div>
                </EditForm>
            </div>
            <div class="giz-client-dialog__footer">
                <Button IsDisabled="@(!_isLoaded || !(ViewState.IsInitialized == true) || ViewState.IsLoading || ViewState.IsValid == false)"
                        Color="ButtonColors.Accent"
                        Size="ButtonSizes.Large"
                        IsFullWidth="true"
                        @onclick="@(() => UserChangeProfileViewStateService.SubmitAsync())"
                        Text="@LocalizationService.GetString("GIZ_GEN_SAVE")" />
            </div>
        }
        else
        {
            @if (ViewState.HasError)
            {
                <div class="giz-client-alert-dialog">
                    <div class="giz-client-alert-dialog__icon giz-client-alert-dialog__icon--error">
                        <Icon Size="IconSizes.Large" SVGIcon="Icons.Close_Client" />
                    </div>
                    <div class="giz-client-alert-dialog__body giz-scrollbar--v">
                        <div class="giz-client-alert-message">@ViewState.ErrorMessage</div>
                    </div>
                    <div class="giz-client-alert-dialog__footer">
                        <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog())" Text="@LocalizationService.GetString("GIZ_GEN_CLOSE")" />
                    </div>
                </div>
            }
            else
            {
                <div class="giz-client-alert-dialog">
                    <div class="giz-client-alert-dialog__icon giz-client-alert-dialog__icon--success">
                        <Icon Size="IconSizes.Large" SVGIcon="Icons.Check" />
                    </div>
                    <div class="giz-client-alert-dialog__body">
                        @LocalizationService.GetString("GIZ_GEN_SUCCESS")
                    </div>
                    <div class="giz-client-alert-dialog__footer">
                        <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog())" Text="@LocalizationService.GetString("GIZ_GEN_CLOSE")" />
                    </div>
                </div>
            }
        }

    </div>
</HostedDialog>
