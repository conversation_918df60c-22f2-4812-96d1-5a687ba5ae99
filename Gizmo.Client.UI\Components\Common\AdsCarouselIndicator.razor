﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase

<div class="giz-ads-carousel-indicators">

    <div class="giz-ads-carousel-indicators-button">
        <IconButton Variant="ButtonVariants.Text" Size="ButtonSizes.Small" SVGIcon="Icons.ArrowBack_Client" @onclick="OnClickPreviousButtonHandler" />
    </div>

    @if (Size < Maximum)
    {
        <div class="giz-ads-carousel-indicators-list" style="width: @GetIndicatorListSize().ToString(System.Globalization.CultureInfo.InvariantCulture)rem;">
            @for (int i = 0; i < Size; i++)
            {
                var local_i = i;
                <div class="giz-ads-carousel-indicator @GetIndicatorClass(local_i)"
                     style="left: @GetIndicatorPosition(local_i).ToString(System.Globalization.CultureInfo.InvariantCulture)rem;"
                     @onclick="@(() => OnClickButton(local_i))">
                </div>
            }
        </div>
    }

    <div class="giz-ads-carousel-indicators-button">
        <IconButton Variant="ButtonVariants.Text" Size="ButtonSizes.Small" SVGIcon="Icons.ArrowForward_Client" @onclick="OnClickNextButtonHandler" />
    </div>

</div>