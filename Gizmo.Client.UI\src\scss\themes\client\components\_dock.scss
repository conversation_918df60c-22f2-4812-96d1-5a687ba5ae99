//=============== Dock ================//

.giz-dock {
    display: flex;

    &__body {
        display: grid;
        grid-auto-flow: column;
    }

    &-item {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 0.2rem;
        box-sizing: content-box;

        &-tooltip {
            background-color: #2B2D33;
            box-shadow: 0 0.4rem 2.0rem rgba(0, 0, 0, 0.08);
            border-radius: 0.8rem;
            padding: 1.2rem;
            white-space: nowrap;
            min-width: 23.4rem;
            display: grid;
            grid-template-rows: min-content auto min-content;
            gap: 0.4rem;

            &-wrapper {
                position: absolute;
                top: 100%;
                left: 0;
                padding-top: 0.8rem;
                visibility: hidden;

                &--visible {
                    visibility: visible;
                }
            }

            &__header {
                @include font-s-theme-client($font-weight-bold-theme-client);
            }

            &__body {
                @include font-s-theme-client($font-weight-light-theme-client);
            }

            &__actions {
                justify-self: right;

                .giz-button {
                    padding: 0 1.7rem;
                }
            }
        }
    }
}

.giz-dock-item-content {
    .giz-default-image {
        img {
            filter: drop-shadow(0 0.4rem 0.8rem rgba(0, 0, 0, 0.16));
        }
    }
}