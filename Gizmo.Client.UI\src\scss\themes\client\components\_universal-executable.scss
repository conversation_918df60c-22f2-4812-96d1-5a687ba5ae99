//=============== Universal Executable ================//

.giz-universal-executable {
    position: relative;
    display: grid;
    grid-template-columns: min-content 1fr;
    cursor: pointer;

    &-progress-bar {
        position: absolute;
        z-index: 3;
        bottom: 0.1rem;
        left: 0.7rem;
        width: 4.8rem;
        display: flex;
        justify-content: center;

        .giz-progress-bar {
            width: 100%;
        }
    }

    &__icon {
        position: relative;
        width: 6.2rem;
        height: 6.2rem;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
            width: 4.8rem;
            height: 4.8rem;
            z-index: 2;
        }
    }

    &__description {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
        overflow: hidden;
        padding-right: 1.1rem;

        &__app {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: rgba(255, 255, 255, 0.6);
            @include font-s-theme-client($font-weight-light-theme-client);
        }

        &__exe {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #FAFAFA;
            //Font
            font-weight: 500;
            font-size: 14px;
            line-height: 22px;
        }
    }

    &:before {
        //visibility: hidden;
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        box-sizing: border-box;
        border-radius: 0.8rem;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.13) 0%, rgba(255, 255, 255, 0.085) 50%), linear-gradient(0deg, rgba(255, 255, 255, 0.0605), rgba(255, 255, 255, 0.0605));
        border: 0.1rem solid rgba(255, 255, 255, 0.13);
        opacity: 0;
        transition: opacity 0.2s;
    }

    &:hover {
        &:before {
            opacity: 1;
        }
    }
}