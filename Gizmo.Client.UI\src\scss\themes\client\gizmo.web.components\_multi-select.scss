//=============== MultiSelect ================//

.giz-multi-select {
    [#{$client-theme-comp-attr}] & {

        &.open {
            .giz-input__icon-right {
                transform: rotate(180deg);
            }
        }

        &__content {
            @include font-m-theme-client($font-weight-light-theme-client);
        }

        &__dropdown {
            background-color: #22272b;
            box-shadow: 0 0.2rem 1.6rem rgba(0, 0, 0, 0.25);
            border-radius: 0.8rem;
            padding: 0.8rem;
            border: unset;

            .giz-multi-select-list {
                padding: unset;
                gap: 8px;

                .giz-multi-select-item {
                    border: 1px solid rgba(246, 251, 253, 0.52);
                    border-radius: 4px;
                    padding: 0 12px;
                    @include font-l-theme-client($font-weight-light-theme-client);

                    &.selected {
                        border: 1px solid transparent;
                        background-color: rgba(250, 250, 250, 0.16);
                    }
                }
            }
        }
    }
}