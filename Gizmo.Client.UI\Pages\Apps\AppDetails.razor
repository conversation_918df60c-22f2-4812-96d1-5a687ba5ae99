﻿@namespace Gizmo.Client.UI.Pages
@inherits CustomDOMComponentBase

<div class="giz-app-details">
    <div class="giz-app-details__app">
        <div class="giz-app-details__app__navigation">
            <div @onclick="OnClickBackButtonHandler">
                <div class="giz-app-details__app__navigation__icon">
                    <Icon SVGIcon="Icons.ArrowLeftLong_Client" />
                </div>
                <div class="giz-app-details__app__navigation__label">
                    @LocalizationService.GetString("GIZ_GEN_BACK")
                </div>
            </div>
        </div>
        <div class="giz-app-details__app__info">
            <div class="giz-app-details__app__info__side">

                <div class="giz-app-details__app__info__image">

                    <GizImage ImageType="Gizmo.UI.ImageType.Application" ImageId="@ViewState.Application.ImageId">
                        <EmptyResultPlaceholder>
                            <div class="giz-default-image">
                                <img src="_content/Gizmo.Client.UI/img/no-app-image.svg" alt='loading' />
                            </div>
                        </EmptyResultPlaceholder>
                    </GizImage>

                </div>

                <div class="giz-app-details-card-brand-info">
                    <div class="giz-app-details-card-brand-title">
                        @LocalizationService.GetString("GIZ_APPS_PUBLISHER")
                    </div>
                    <div class="giz-app-details-card-brand-text">
                        @if (ViewState.Application?.PublisherId != null)
                        {
                            <AppDetailsAppPublisher @key="ViewState.Application.PublisherId.Value" PublisherId="@ViewState.Application.PublisherId.Value" />
                        }
                    </div>
                    <div class="giz-app-details-card-brand-title">
                        @LocalizationService.GetString("GIZ_APPS_RELEASE_DATE")
                    </div>
                    <div class="giz-app-details-card-brand-text">
                        @ViewState.Application?.ReleaseDate?.ToShortDateString()
                    </div>
                    <div class="giz-app-details-card-brand-title">
                        @LocalizationService.GetString("GIZ_APPS_DATE_ADDED")
                    </div>
                    <div class="giz-app-details-card-brand-text">
                        @ViewState.Application?.AddDate.ToShortDateString()
                    </div>
                    @*<div class="giz-app-details-card-brand-title">
                    @LocalizationService.GetString("GIZ_APPS_PLAYER_MODE")
                    </div>
                    <div class="giz-app-details-card-brand-text">
                    <div class="d-flex align-center" style="justify-content: flex-end;">
                    Single Player
                    </div>
                    </div>*@
                </div>

            </div>

            <div class="giz-app-details__app__info__main giz-scrollbar--v">

                <div class="giz-app-details__app__info__details">
                    @if (ViewState.Application != null)
                    {
                        <AppDetailsAppCategory @key="ViewState.Application.ApplicationCategoryId" ApplicationCategoryId="@ViewState.Application.ApplicationCategoryId" />
                    }
                    <div class="giz-app-details__app__info__details__title">
                        @ViewState.Application?.Title
                    </div>
                    <div class="giz-app-details__app__info__details__description">
                        @ViewState.Application?.Description
                    </div>
                    <div class="giz-app-details__app__info__details__executables">
                        @if (_showMore)
                        {
                            @foreach (var executable in ViewState.Executables)
                            {
                                <UniversalExecutable @key="executable.ExecutableId" ExecutableId="@executable.ExecutableId" ShowProgressBar="true" ShowExeName="true" />
                            }
                        }
                        else
                        {
                            @foreach (var executable in ViewState.Executables.Take(4))
                            {
                                <UniversalExecutable @key="executable.ExecutableId" ExecutableId="@executable.ExecutableId" ShowProgressBar="true" ShowExeName="true" />
                            }
                        }
                    </div>
                    
                    @if (!_showMore && ViewState.Executables.Count() > 4)
                    {
                        <div>
                            <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Small" @onclick="@(() => OnClickMainButtonHandler())">@LocalizationService.GetString("GIZ_APPS_SHOW_MORE")</Button>
                        </div>
                    }

                </div>

                <div class="giz-app-details__app__info__additional-content">
                    @*<div class="giz-app-details__app__info__additional-content__tags">
                        @foreach (var item in Enum.GetValues(typeof(ApplicationModes)).OfType<ApplicationModes>())
                        {
                            <div class="giz-app-tag">@LocalizationService.GetString(item)</div>
                        }
                    </div>*@
                    @if (_hasMedia || _hasLinks)
                    {
                        <div class="giz-app-details__app__info__additional-content__tab">
                            <ClientTab>
                                @if (_hasMedia)
                                {
                                    <ClientTabItem Class="@(_selectedTabIndex == 0 ? "active" : "")" OnClick=@(() => SelectTab(0))>
                                        <div>
                                            @LocalizationService.GetString("GIZ_APPS_MEDIA")
                                        </div>
                                    </ClientTabItem>
                                }
                                @if (_hasLinks)
                                {
                                    <ClientTabItem Class="@(_selectedTabIndex == 1 ? "active" : "")" OnClick=@(() => SelectTab(1))>
                                        <div>
                                            @LocalizationService.GetString("GIZ_APPS_LINKS")
                                        </div>
                                    </ClientTabItem>
                                }
                            </ClientTab>
                        </div>
                        <div class="giz-app-details__app__info__additional-content__media">
                            @if (_selectedTabIndex == 0)
                            {
                                <AppDetailsMediaItems ApplicationId="@ApplicationId" @key="ApplicationId" />
                            }
                            else
                            {
                                <AppDetailsLinks ApplicationId="@ApplicationId" @key="ApplicationId" />
                            }
                        </div>
                    }
                </div>

            </div>
        </div>
    </div>
    <div class="giz-app-details__leaderboard">
        @*<Leaderboard />*@
    </div>
</div>
