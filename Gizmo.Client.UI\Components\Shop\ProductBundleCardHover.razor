﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase
@using System.Globalization

<div class="giz-timeline">
    <div class="giz-timeline-header">
        <HexagonIcon />
        <div>@LocalizationService.GetString("GIZ_SHOP_CARD_BUNDLE_BUNDLE_DESCRIPTION")</div>
    </div>

    @if (Product != null)
    {
        <div class="giz-timeline-item">
            <div class="giz-time-product-details">
                <div>@LocalizationService.GetString("GIZ_PRODUCT_TIME_EXPIRATION_BUYING_TIME")</div>
                <div class="giz-time-product-time-availabile">
                    @if (Product.PurchaseAvailability != null)
                    {
                        @ProductHelpers.GetPurchaseAvailabilities(Product, true, LocalizationService).FirstOrDefault()
                    }
                    else
                    {
                        <Icon SVGIcon="Icons.Infinite" Size="IconSizes.Small" />
                    }
                </div>
            </div>
        </div>

        @foreach (var bundledProduct in Product.BundledProducts)
        {
            <ProductBundleCardBundledProduct @key="bundledProduct.Id" ProductId="@bundledProduct.Id" Quantity="@bundledProduct.Quantity" />
        }
    }
</div>