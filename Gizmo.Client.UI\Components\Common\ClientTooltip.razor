﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase

<div class="@Class @ClassName"
     @onmouseover="OnMouseOverHandler"
     @onmouseout="OnMouseOutHandler"
     id="@Id"
     @ref="@Ref">
    @ChildContent
    @if (!String.IsNullOrEmpty(Text) || TooltipContent != null)
    {
        <div class="@TooltipClassName"
             style="@TooltipStyleValue"
             @ref="@_tooltipContent">
            @if (TooltipContent != null)
            {
                @TooltipContent
            }
            else
            {
                @Text
            }
            <div class="giz-client-tooltip-pin"
                 style="@TooltipPinStyleValue">
            </div>
        </div>
    }
</div>
