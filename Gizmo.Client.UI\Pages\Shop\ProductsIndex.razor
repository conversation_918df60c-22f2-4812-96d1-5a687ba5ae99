﻿@namespace Gizmo.Client.UI.Pages
@inherits CustomDOMComponentBase
@using Gizmo.Client.UI.ViewModels

<div class="giz-shop-wrapper">
    <div class="giz-shop">
        <div class="giz-shop__products">
            <div class="giz-shop__products__header">
                <div class="giz-shop__products__header__tab">
                    <ClientTab>
                        <ClientTabItem Class="@(!ViewState.SelectedUserProductGroupId.HasValue ? "active" : "")">
                            <div @onclick=@(() => ShopService.UpdateUserGroupedProductsAsync(null))>
                                @LocalizationService.GetString("GIZ_GEN_ALL")
                            </div>
                        </ClientTabItem>
                        @if (ViewState.UserProductGroups != null)
                        {
                            @foreach (var productGroup in ViewState.UserProductGroups)
                            {
                                <ClientTabItem Class="@(productGroup.ProductGroupId == ViewState.SelectedUserProductGroupId ? "active" : "")" OnClick=@(() => ShopService.UpdateUserGroupedProductsAsync(productGroup.ProductGroupId))>
                                    <div>
                                        @productGroup.Name
                                    </div>
                                </ClientTabItem>
                            }
                        }
                    </ClientTab>
                </div>
            </div>
            <div class="giz-shop__products__body giz-ads-container giz-scrollbar--v">
                @if (AdvertisementsViewState.Advertisements.Count() > 0)
                {
                    <div class="giz-shop__products__body__ads">
                        <ExpansionPanel IsCollapsed="@AdvertisementsViewState.IsCollapsed" IsCollapsedChanged="@((value) => AdvertisementsViewStateService.SetCollapsed(value))">
                            <ExpansionPanelHeader>
                                @LocalizationService.GetString("GIZ_GEN_ADS")
                            </ExpansionPanelHeader>
                            <ChildContent>
                                <AdsCarousel Interval="5000" />
                            </ChildContent>
                        </ExpansionPanel>
                    </div>
                }
                @if (!string.IsNullOrEmpty(ViewState.SearchPattern))
                {
                    <div class="giz-section">
                        <div class="giz-section__header__filters">
                            <div>
                                <div class="giz-section__header__filters__title">@LocalizationService.GetString("GIZ_GEN_RESULTS_FOR")</div>
                                <div>
                                    <Chip CanClose="true" OnClose="@(() => ShopService.ClearSearchPattern() )">@ViewState.SearchPattern</Chip>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (ViewState.UserGroupedProducts.Count() == 0)
                    {
                        <div class="giz-section__body">
                            <div class="global-search-no-results">
                                <div class="global-search-no-results__header">@LocalizationService.GetString("GIZ_GLOBAL_SEARCH_NO_MATCH_FOUND_FOR") <span class="global-search-pattern">@ViewState.SearchPattern</span></div>
                                <div class="global-search-no-results__body">@LocalizationService.GetString("GIZ_GLOBAL_SEARCH_NO_MATCH_FOUND_MESSAGE")</div>
                            </div>
                        </div>
                    }
                }
                @foreach (var productGroup in ViewState.UserGroupedProducts)
                {
                    <div class="giz-section">
                        <ProductsIndexSectionHeader @key="productGroup.Key" ProductGroupId="@productGroup.Key" />
                        <div class="giz-section__body">
                            <ProductVirtualizedCards Items="productGroup" ColumnsCount="PopularItemsOptions.Value.ProductsPageMaxItemsPerRow" />
                        </div>
                    </div>
                }
            </div>

        </div>
        <div class="giz-shop__order">
            <GizOrder />
        </div>
    </div>
</div>
