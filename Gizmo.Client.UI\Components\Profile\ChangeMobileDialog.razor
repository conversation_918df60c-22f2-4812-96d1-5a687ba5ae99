﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase
@using Microsoft.AspNetCore.Components.Forms

<HostedDialog>
    <div class="giz-client-dialog giz-change-mobile-dialog">
        <div class="giz-client-dialog__header">
            <div class="giz-client-dialog__header__title">@LocalizationService.GetString("GIZ_CHANGE_MOBILE")</div>
            <IconButton SVGIcon="Icons.Close" Variant="ButtonVariants.Text" Size="ButtonSizes.Small" Class="close-btn" @onclick="() => CloseDialog()" />
        </div>
        @switch (ViewState.PageIndex)
        {
            case 0:

                <div class="giz-client-dialog__body">
                    <div class="giz-change-mobile-dialog-message">@LocalizationService.GetString("GIZ_CHANGE_MOBILE_MESSAGE")</div>
                    <EditForm EditContext="@UserChangeMobileService.EditContext">
                        <div class="giz-registration-form-input">
                            <IconSelect Placeholder="@LocalizationService.GetString("GIZ_GEN_SEARCH")"
                                        IsVirtualized="false"
                                        IsLoading="@(!_isLoaded)" 
                                        CanClearValue="@(!(GetSelectedCountry()?.PhonePrefix == "+"))"
                                        OnClickClearValueButton="OnClickClearValueButtonHandler"
                                        IsTransparent="true"
                                        HandleSVGIcon="Icons.Select_Client"
                                        TValue="IconSelectCountry"
                                        ItemSource="@Countries"
                                        SelectedItem="@GetSelectedCountry()"
                                        SelectedItemChanged="@SetSelectedCountry"
                                        Size="InputSizes.Medium"
                                        IsFullWidth="true"
                                        Label="@LocalizationService.GetString("GIZ_USER_COUNTRY")"
                                        OpenDirection="PopupOpenDirections.Cursor"
                                        MaximumHeight="20.0rem"
                                        PopupClass="giz-scrollbar--v" />
                        </div>
                        <div class="giz-form-input">
                            <MaskedPhoneInput IsTransparent="true"
                                              Mask="###-###-####"
                                              Prefix="@ViewState.Prefix"
                                              AllowMoreDigits="@(string.IsNullOrEmpty(ViewState.Prefix) || ViewState.Prefix == "+")"
                                              Value="@ViewState.MobilePhone"
                                              ValueChanged="@((string value) => UserChangeMobileService.SetMobilePhone(value))"
                                              ValueExpression="@(() => ViewState.MobilePhone)"
                                              Size="InputSizes.Medium"
                                              IsFullWidth="true"
                                              Label="@LocalizationService.GetString("GIZ_GEN_PHONE_NUMBER")" />
                        </div>
                    </EditForm>
                </div>
                <div class="giz-client-dialog__footer">
                    <Button IsDisabled="@(ViewState.IsLoading)" 
                            Color="ButtonColors.Accent"
                            Size="ButtonSizes.Large"
                            IsFullWidth="true"
                            @onclick="@(() => UserChangeMobileService.SendConfirmationCodeAsync())">
                        <ChildContent>
                            @if (ViewState.IsLoading)
                            {
                                <Spinner />
                            }
                            else
                            {
                                <div>@LocalizationService.GetString("GIZ_USER_CONFIRMATION_GET_CONFIRMATION_CODE")</div>
                            }
                        </ChildContent>
                    </Button>
                </div>

                break;

            case 1:

                <div class="giz-client-dialog__body">
                    <div class="giz-change-mobile-dialog-message">@LocalizationService.GetString("GIZ_CHANGE_MOBILE_SUCCESS_MESSAGE")</div>
                    <div class="giz-change-mobile-dialog-mobile">TODO: A...</div>
                    <EditForm EditContext="@UserChangeMobileService.EditContext">
                        <div class="giz-form-input">
                            <TextInput IsTransparent="true"
                                       Value="@ViewState.ConfirmationCode"
                                       ValueChanged="@((string value) => UserChangeMobileService.SetConfirmationCode(value))"
                                       ValueExpression="@(() => ViewState.ConfirmationCode)"
                                       Size="InputSizes.Medium"
                                       IsFullWidth="true"
                                       Label="@LocalizationService.GetString("GIZ_USER_CONFIRMATION_ENTER_CONFIRMATION_CODE")" />
                        </div>
                    </EditForm>
                </div>
                <div class="giz-client-dialog__footer">
                    <Button Color="ButtonColors.Accent" Size="ButtonSizes.Large" IsFullWidth="true" @onclick="@(() => UserChangeMobileService.VerifyAsync())">@LocalizationService.GetString("GIZ_GEN_VERIFY")</Button>
                    <div class="giz-resend-button">
                        <Button Variant="ButtonVariants.Outline"
                                Size="ButtonSizes.Large"
                                IsFullWidth="true"
                                IsDisabled="@(!ViewState.CanResend)"
                                Text="@LocalizationService.GetString("GIZ_GEN_RESEND")"
                                @onclick="@(() => UserChangeMobileService.ResendAsync())" />
                        <div class="giz-resend-button__time">@string.Format("{0:mm\\:ss}", ViewState.ResendTimeLeft)</div>
                    </div>
                </div>

                break;
                
            case 2:
                
                <div>Well done!</div>

                break;
        }
    </div>
</HostedDialog>
