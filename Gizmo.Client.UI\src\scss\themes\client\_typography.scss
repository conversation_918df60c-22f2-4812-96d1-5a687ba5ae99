//=============== Theme Client Typography ================//

$font-family-header-theme-client: Rubik, sans-serif;
$font-family-text-theme-client: 'Noto Sans', sans-serif;

$font-weight-light-theme-client: 400;
$font-weight-regular-theme-client: 500;
$font-weight-bold-theme-client: 700;

@font-face {
    font-family: 'Rubik';
    font-style: normal;
    font-weight: 400;
    src: url('../font-family/Rubik-Regular.ttf');
}

@font-face {
    font-family: 'Rubik';
    font-style: normal;
    font-weight: 500;
    src: url('../font-family/Rubik-Medium.ttf');
}

@font-face {
    font-family: 'Rubik';
    font-style: normal;
    font-weight: 700;
    src: url('../font-family/Rubik-Bold.ttf');
}

@font-face {
    font-family: 'Noto Sans';
    font-style: normal;
    font-weight: 400;
    src: url('../font-family/NotoSans-Regular.ttf');
}

@font-face {
    font-family: 'Noto Sans';
    font-style: normal;
    font-weight: 500;
    src: url('../font-family/NotoSans-Medium.ttf');
}

@font-face {
    font-family: 'Noto Sans';
    font-style: normal;
    font-weight: 700;
    src: url('../font-family/NotoSans-Bold.ttf');
}

@mixin font-h1-theme-client($font-weight: 500) {
    font-weight: $font-weight;
    font-size: 3.2rem;
    line-height: 4.0rem;
    letter-spacing: initial;
    font-family: $font-family-header-theme-client;
}

@mixin font-h5-theme-client($font-weight: 500) {
    font-weight: $font-weight;
    font-size: 2.4rem;
    line-height: 3.0rem;
    letter-spacing: initial;
    font-family: $font-family-header-theme-client;
}

@mixin font-s-theme-client($font-weight: 500) {
    font-weight: $font-weight;
    font-size: 1.2rem;
    line-height: 1.8rem;
    letter-spacing: initial;
}

@mixin font-m-theme-client($font-weight: 500) {
    font-weight: $font-weight;
    font-size: 1.4rem;
    line-height: 2.2rem;
    letter-spacing: initial;
}

@mixin font-l-theme-client($font-weight: 500) {
    font-weight: $font-weight;
    font-size: 1.6rem;
    line-height: 2.6rem;
    letter-spacing: initial;
}

@mixin font-xl-theme-client($font-weight: 500) {
    font-weight: $font-weight;
    font-size: 1.8rem;
    line-height: 2.8rem;
    letter-spacing: initial;
}

@mixin font-xxl-theme-client($font-weight: 500) {
    font-weight: $font-weight;
    font-size: 2.0rem;
    line-height: 3.0rem;
    letter-spacing: initial;
}
