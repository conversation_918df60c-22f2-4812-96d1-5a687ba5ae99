﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase
@using System.Globalization

<div class="giz-product-card time"
     @onclick="OpenDetails">
    <div class="giz-product-card__content">

        <div class="giz-product-card__content__image giz-product-card__content__image--time">
            <div class="giz-product-card__content__image__hovered">
                <div class="giz-product-card__content__image__hovered__header">

                    <div class="giz-product-card__content--hovered">
                        <ProductTimeCardHover @key="Product.Id" Product="@Product" />
                    </div>

                    <div class="giz-product-card-award-indicator">
                        @if (Product.UnitPointsAward > 0)
                        {
                            <PointsAward2Icon />
                        }
                    </div>

                </div>
            </div>

            <GizImage ImageType="Gizmo.UI.ImageType.ProductDefault" ImageId="@Product.DefaultImageId" ImageFitType="ImageFitType.Cover">
                <EmptyResultPlaceholder>
                    <div class="giz-product-time-image-wrapper">
                        <div class="giz-product-time-image">
                            <div class="giz-default-image">
                                <img src="_content/Gizmo.Client.UI/img/@ProductHelpers.GetProductTimeImage(Product)" alt='loading' />
                            </div>
                            <div class="giz-product-time-image__time">
                                <div class="giz-product-time-image__time__number">@ProductHelpers.GetProductTimeNumber(Product)</div>
                                <div class="giz-product-time-image__time__text">@ProductHelpers.GetProductTimeText(Product, LocalizationService)</div>
                            </div>
                        </div>
                    </div>
                </EmptyResultPlaceholder>
            </GizImage>

        </div>

        <div class="giz-product-card__content__details">
            <div class="giz-product-card__price">
                <ProductCardPrice @key="Product.Id" Product="@Product" />
            </div>
            <div class="giz-product-card__title">
                @Product.Name
            </div>
            <ProductTimeHostGroups @key="Product.Id" Product="@Product" />
        </div>
        <div class="giz-product-card__content__footer">
            <ProductQuantityPicker @key="Product.Id" ProductId="@Product.Id" OnClick="@Ignore" />
        </div>
    </div>
</div>