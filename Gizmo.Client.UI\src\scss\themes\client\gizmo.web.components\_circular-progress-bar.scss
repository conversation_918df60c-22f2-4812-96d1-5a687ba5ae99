//=============== CircularProgressBar ================//

.giz-circular-progress-bar {
    [#{$client-theme-comp-attr}] & {
        .giz-circular-progress-bar-bar {
            top: 0;
        }

        &.giz-circular-progress-bar--extra-small {
            width: 1.2rem;
            height: 1.2rem;

            &::after {
                border-radius: 50%;
                top: 0.1rem;
                left: 0.1rem;
                border-width: 0.16rem;
                width: calc(100% - 0.2rem);
                height: calc(100% - 0.2rem);
                box-sizing: border-box;
            }

            & > .giz-circular-progress-bar--left {

                & > .giz-circular-progress-bar-bar {
                    border-top-right-radius: 0.6rem;
                    border-bottom-right-radius: 0.6rem;
                    border-width: 0.2rem;
                }
            }

            & > .giz-circular-progress-bar--right {

                & > .giz-circular-progress-bar-bar {
                    border-top-left-radius: 0.6rem;
                    border-bottom-left-radius: 0.6rem;
                    border-width: 0.2rem;
                }
            }

            & > .giz-circular-progress-bar--value {
                //font-size: 0.8rem;
            }
        }

        &.giz-circular-progress-bar--small {
            width: 2.4rem;
            height: 2.4rem;

            &::after {
                border-radius: 50%;
                top: 0;
                left: 0;
                border-width: 0.4rem;
                width: calc(100% - 0.8rem);
                height: calc(100% - 0.8rem);
            }

            & > .giz-circular-progress-bar--left {

                & > .giz-circular-progress-bar-bar {
                    border-top-right-radius: 1.2rem;
                    border-bottom-right-radius: 1.2rem;
                    border-width: 0.4rem;
                }
            }

            & > .giz-circular-progress-bar--right {

                & > .giz-circular-progress-bar-bar {
                    border-top-left-radius: 1.2rem;
                    border-bottom-left-radius: 1.2rem;
                    border-width: 0.4rem;
                }
            }

            & > .giz-circular-progress-bar--value {
                //font-size: 0.8rem;
            }
        }
    }
}

