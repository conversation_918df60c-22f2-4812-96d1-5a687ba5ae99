﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase

<div class="@Class @ClassName" @onclick="OnClickHandle">
    <div class="giz-news-rotator-item__image">
        <div class="giz-news-rotator-item__image__feed">
            @if (!string.IsNullOrEmpty(_image))
            {
                <img src="@_image" />
            }
            else
            {
                <img src="_content/Gizmo.Client.UI/img/no-image.svg" alt='loading' class="giz-no-image" />
            }
        </div>
    </div>
    <div class="giz-news-rotator-item__message">
        @if (!string.IsNullOrEmpty(_summary))
        {
            @((MarkupString)_summary)
        }
        else
        {
            @((MarkupString)_title)
        }
    </div>
</div>