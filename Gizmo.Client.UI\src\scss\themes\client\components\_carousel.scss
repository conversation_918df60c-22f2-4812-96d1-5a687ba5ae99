//=============== Carousel ================//

.giz-carousel {
    display: grid;
    grid-template-rows: 1fr min-content;
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;

    &__navigation {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 2.4rem;
        color: rgba(255, 255, 255, 0.3);
        @include font-s-theme-client($font-weight-regular-theme-client);
    }

    &-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 3.2rem;

        &-button {
            box-sizing: content-box;
            width: 0.8rem;
            height: 0.8rem;
            border-radius: 50%;
            background-color: rgba(250, 250, 250, 0.16);
            cursor: pointer;

            &.active {
                background-color: #FAFAFA;
                border: 0.4rem solid #0078D2;
            }
        }
    }

    &__content {
        position: relative;
        max-height: 100%;
        overflow: hidden;
    }

    &-item {
        display: none;
        position: absolute;
        top: 0;
        width: 100%;
        height: 100%;

        &--active {
            display: inline-block;
        }

        &--current {
            position: relative;
        }
    }

    .slide-left-current {
        left: -100%;
        animation-duration: 1s;
        animation-name: carousel-slide-left-current-anim;
    }

    .slide-left-next {
        animation-duration: 1s;
        animation-name: carousel-slide-left-next-anim;
    }

    .slide-right-current {
        right: -100%;
        animation-duration: 1s;
        animation-name: carousel-slide-right-current-anim;
    }

    .slide-right-next {
        animation-duration: 1s;
        animation-name: carousel-slide-right-next-anim;
    }
}

@keyframes carousel-slide-left-current-anim {
    from {
        left: 0;
    }

    to {
        left: -100%;
    }
}

@keyframes carousel-slide-left-next-anim {
    from {
        left: 100%;
    }

    to {
        left: 0;
    }
}

@keyframes carousel-slide-right-current-anim {
    from {
        right: 0;
    }

    to {
        right: -100%;
    }
}

@keyframes carousel-slide-right-next-anim {
    from {
        right: 100%;
    }

    to {
        right: 0;
    }
}