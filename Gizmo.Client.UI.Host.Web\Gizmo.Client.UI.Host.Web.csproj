﻿<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<!--Fixes duplicate file error on publish (.NET6 BUG)-->
		<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
		<UseBlazorWebAssembly>true</UseBlazorWebAssembly>
    <BlazorWebAssemblyLoadAllGlobalizationData>true</BlazorWebAssemblyLoadAllGlobalizationData>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="6.0.7" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="6.0.7" PrivateAssets="all" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="6.0.0" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Gizmo.Client.UI\Gizmo.Client.UI.csproj" />
	</ItemGroup>

</Project>
