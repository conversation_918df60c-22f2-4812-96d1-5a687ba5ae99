//=============== Ads Carousel ================//

.giz-ads-carousel {
    display: grid;
    grid-template-rows: min-content 1fr min-content;
    position: relative;
    overflow: hidden;
    width: 100%;
    padding-top: 1.6rem;

    &__header {
        margin-bottom: 1.6rem;
        @include font-h5-theme-client($font-weight-bold-theme-client);
    }

    &__body {
        position: relative;
        height: 40rem;
        overflow: hidden;
    }

    &__navigation {
        margin-top: 2.1rem;
    }

    &-item {
        position: absolute;
        border-radius: 0.8rem;
        overflow: hidden;
        background-color: #22272b;
        display: none;

        &--media {
            cursor: pointer;
        }

        &-dialog {
            &.giz-client-dialog {
                width: 120rem;
                height: 80rem;
            }

            &-frame {
                width: 100%;
                height: 100%;
            }
        }

        &-command {
            //visibility: hidden;
            pointer-events: all;
            z-index: 1;
        }

        &:hover {
            .giz-ads-carousel-item-command {
                //visibility: visible;
            }
        }

        &.previous-out {
            display: inline-block;
            width: 72.2rem;
            height: 38rem;
            top: 1rem;
            left: 50%;
            transform: translateX(-100%);
            //mask-image: linear-gradient( to left, rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.4) );
            transition: opacity 0.2s;
            opacity: 0.8;
            z-index: 1;
            pointer-events: none;

            &:after {
                content: "";
                background-color: black;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                opacity: 0.2;
                transition: opacity 0.2s;
                z-index: 2;
                cursor: pointer;
                pointer-events: all;
            }

            &:hover {
                opacity: 1;

                &:after {
                    opacity: 0;
                }
            }

            .giz-ads-carousel-item__content {
                //visibility: hidden;
            }

            .giz-ads-carousel-item__content__actions {
                display: none;
            }
        }

        &.previous {
            display: inline-block;
            width: 72.2rem;
            height: 38rem;
            top: 1rem;
            left: 50%;
            transform: translateX(-100%);
            //mask-image: linear-gradient( to left, rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.4) );
            transition: opacity 0.2s;
            opacity: 0.8;
            z-index: 2;
            pointer-events: none;

            &:after {
                content: "";
                background-color: black;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                opacity: 0.2;
                transition: opacity 0.2s;
                z-index: 2;
                cursor: pointer;
                pointer-events: all;
            }

            &:hover {
                opacity: 1;

                &:after {
                    opacity: 0;
                }
            }

            .giz-ads-carousel-item__content {
                //visibility: hidden;
            }

            .giz-ads-carousel-item__content__actions {
                display: none;
            }
        }

        &.current {
            display: inline-block;
            width: 76rem;
            height: 40rem;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            z-index: 3;
        }

        &.next {
            display: inline-block;
            width: 72.2rem;
            height: 38rem;
            top: 1rem;
            left: 50%;
            transform: translateX(0%);
            //mask-image: linear-gradient( to right, rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.4) );
            transition: opacity 0.2s;
            opacity: 0.8;
            z-index: 2;
            pointer-events: none;

            &:after {
                content: "";
                background-color: black;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                opacity: 0.2;
                transition: opacity 0.2s;
                z-index: 2;
                cursor: pointer;
                pointer-events: all;
            }

            &:hover {
                opacity: 1;

                &:after {
                    opacity: 0;
                }
            }

            .giz-ads-carousel-item__content {
                //visibility: hidden;
            }

            .giz-ads-carousel-item__content__actions {
                display: none;
            }
        }

        &.next-out {
            display: inline-block;
            width: 72.2rem;
            height: 38rem;
            top: 1rem;
            left: 50%;
            transform: translateX(0%);
            //mask-image: linear-gradient( to right, rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.4) );
            transition: opacity 0.2s;
            opacity: 0.8;
            z-index: 1;
            pointer-events: none;

            &:after {
                content: "";
                background-color: black;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                opacity: 0.2;
                transition: opacity 0.2s;
                z-index: 2;
                cursor: pointer;
                pointer-events: all;
            }

            &:hover {
                opacity: 1;

                &:after {
                    opacity: 0;
                }
            }

            .giz-ads-carousel-item__content {
                //visibility: hidden;
            }

            .giz-ads-carousel-item__content__actions {
                display: none;
            }
        }

        &__image {
            position: relative;
            width: 100%;
            height: 100%;
            //border-radius: 0.8rem;
            //overflow: hidden;

            img {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 2;

                &:after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: #22272b;
                }
            }

            .giz-broken-image {
                width: unset;
                height: unset;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 1;
            }
        }

        &__content {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 1rem;
            z-index: 3;
            @include font-m-theme-client($font-weight-light-theme-client);

            &__body {
                max-height: 100%;
                overflow: hidden;
            }

            &__actions {
                position: absolute;
                bottom: 3.2rem;
                left: 3.2rem;
                right: 3.2rem;
                display: flex;
                justify-content: space-between;

                .giz-button {
                    padding: 0.9rem 5.1rem;
                    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.08), 0px 8px 24px rgba(0, 0, 0, 0.32);

                    .giz-icon {
                        margin-right: 0.6rem;
                    }
                }

                .giz-ads-carousel-item-command--media {
                    &.giz-button {
                        [#{$client-theme-comp-attr}] & {
                            &--fill {
                                &.primary:not(.disabled) {
                                    padding: 0;
                                    width: 4rem;
                                    //background-color: rgba(250, 250, 250, 0.16);

                                    &:hover {
                                        //background-color: rgba(250, 250, 250, 0.23);
                                    }

                                    &:active {
                                        //background-color: rgba(250, 250, 250, 0.23);

                                        .giz-icon {
                                            svg {
                                                //opacity: 0.9;
                                            }
                                        }
                                    }

                                    .giz-icon {
                                        margin-right: 0;

                                        svg {
                                            //opacity: 0.75;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    &-indicators {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1.4rem;

        &-button {
            .giz-icon-button--small {
                width: 2.4rem;
                height: 2.4rem;
                min-width: 2.4rem;
                min-height: 2.4rem;
                color: #999999;

                svg {
                    path {
                        stroke: #999999;
                    }
                }
            }
        }

        &-list {
            position: relative;
            height: 1.6rem;
        }
    }

    &-indicator {
        position: absolute;
        box-sizing: content-box;
        width: 1.6rem;
        height: 1.6rem;
        border-radius: 50%;
        cursor: pointer;
        background-color: transparent;

        &:after {
            position: absolute;
            border-radius: 50%;
            content: "";
            top: 0.4rem;
            left: 0.4rem;
            width: calc(100% - 0.8rem);
            height: calc(100% - 0.8rem);
            background-color: rgba(250, 250, 250, 0.16);
        }

        &.active {
            background-color: #0078d2;

            &:after {
                background-color: #fafafa;
            }
        }
    }

    &--simple {
        .giz-ads-carousel__body {
            display: flex;
            justify-content: center;
            gap: 2.4rem;
        }

        .giz-ads-carousel-item {
            position: relative;
            display: block;
            width: 76rem;
            height: 40rem;
        }
    }

    .send-to-left {
        z-index: 1;
        animation-duration: 0.5s;
        animation-name: ads-send-to-left-anim;
        animation-timing-function: ease-in-out;
        animation-fill-mode: forwards;
    }

    .send-to-right {
        z-index: 1;
        animation-duration: 0.5s;
        animation-name: ads-send-to-right-anim;
        animation-timing-function: ease-in-out;
        animation-fill-mode: forwards;
    }

    .send-to-back {
        animation-duration: 0.5s;
        animation-name: ads-send-to-back-anim;
        animation-timing-function: ease-in-out;
        animation-fill-mode: forwards;
    }

    .bring-to-front-left {
        animation-duration: 0.5s;
        animation-name: ads-bring-to-front-left-anim;
        animation-timing-function: ease-in-out;
        animation-fill-mode: forwards;
    }

    .bring-to-front-right {
        animation-duration: 0.5s;
        animation-name: ads-bring-to-front-right-anim;
        animation-timing-function: ease-in-out;
        animation-fill-mode: forwards;
    }

    .send-to-back-left {
        animation-duration: 0.5s;
        animation-name: ads-send-to-back-left-anim;
        animation-timing-function: ease-in-out;
        animation-fill-mode: forwards;
    }

    .send-to-back-right {
        animation-duration: 0.5s;
        animation-name: ads-send-to-back-right-anim;
        animation-timing-function: ease-in-out;
        animation-fill-mode: forwards;
    }
}

@keyframes ads-send-to-left-anim {
    from {
        transform: translateX(0%);
    }

    to {
        transform: translateX(-100%);
    }
}

@keyframes ads-send-to-right-anim {
    from {
        transform: translateX(-100%);
    }

    to {
        transform: translateX(0%);
    }
}

@keyframes ads-send-to-back-anim {
    from {
        opacity: 0.8;
    }

    to {
        opacity: 0;
    }
}

@keyframes ads-bring-to-front-left-anim {
    from {
        top: 1rem;
        height: 38rem;
        width: 72.2rem;
        transform: translateX(-100%);
        //opacity: 0.8;
    }

    to {
        top: 0rem;
        height: 40rem;
        width: 76rem;
        transform: translateX(-50%);
        //opacity: 1;
    }
}

@keyframes ads-bring-to-front-right-anim {
    from {
        top: 1rem;
        height: 38rem;
        width: 72.2rem;
        transform: translateX(0%);
        //opacity: 0.8;
    }

    to {
        top: 0rem;
        height: 40rem;
        width: 76rem;
        transform: translateX(-50%);
        //opacity: 1;
    }
}

@keyframes ads-send-to-back-left-anim {
    from {
        top: 0rem;
        height: 40rem;
        width: 76rem;
        transform: translateX(-50%);
    }

    to {
        top: 1rem;
        height: 38rem;
        width: 72.2rem;
        transform: translateX(-100%);
    }
}

@keyframes ads-send-to-back-right-anim {
    from {
        top: 0rem;
        height: 40rem;
        width: 76rem;
        transform: translateX(-50%);
    }

    to {
        top: 1rem;
        height: 38rem;
        width: 72.2rem;
        transform: translateX(0%);
    }
}
