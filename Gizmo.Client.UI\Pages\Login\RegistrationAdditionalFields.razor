﻿@namespace Gizmo.Client.UI.Pages
@inherits CustomDOMComponentBase
@layout _Layout_Login
@using System.Threading;
@using Gizmo.UI;
@using Microsoft.AspNetCore.Components.Forms

<LoginCard>
    <CardHeader>
        <div class="giz-login-links">
            <div class="giz-back-button" @onclick="@(() => NavigationService.NavigateTo(ClientRoutes.RegistrationBasicFieldsRoute))">
                <Icon SVGIcon="Icons.ArrowLeft_Client" Size="IconSizes.ExtraSmall" />
            </div>
            <div class="giz-login-new-user">@LocalizationService.GetString("GIZ_LOGIN_ALREADY_A_MEMBER") <a href="@ClientRoutes.LoginRoute">@LocalizationService.GetString("GIZ_REGISTRATION_SIGN_IN")</a></div>
        </div>
        <div class="giz-login-alerts">
            @if (ViewState.HasError)
            {
                <Toast CanClose="true" AlertType="AlertTypes.Danger" Text="@ViewState.ErrorMessage" />
            }
        </div>
    </CardHeader>
    <CardBody>
        <EditForm EditContext="@UserRegistrationAdditionalFieldsService.EditContext">
            <div class="giz-login-title">@LocalizationService.GetString("GIZ_REGISTRATION_SIGN_UP_TITLE")</div>
            @if (UserRegistrationViewState.DefaultUserGroupRequiredInfo.Country)
            {
                @if (UserRegistrationViewState.ConfirmationMethod != RegistrationVerificationMethod.MobilePhone)
                {
                    <div class="giz-registration-form-input">
                        <IconSelect Placeholder="@LocalizationService.GetString("GIZ_GEN_SEARCH")"
                                    IsVirtualized="false"
                                    IsLoading="@(!_isLoaded)"
                                    CanClearValue="@(!(GetSelectedCountry()?.PhonePrefix == "+"))"
                                    OnClickClearValueButton="OnClickClearValueButtonHandler"
                                    HandleSVGIcon="Icons.Select_Client"
                                    TValue="IconSelectCountry"
                                    ItemSource="@Countries"
                                    SelectedItem="@GetSelectedCountry()"
                                    SelectedItemChanged="@SetSelectedCountry"
                                    Size="InputSizes.Small"
                                    IsFullWidth="true"
                                    Label="@LocalizationService.GetString("GIZ_USER_COUNTRY_REGION")"
                                    OpenDirection="PopupOpenDirections.Bottom"
                                    MaximumHeight="20.0rem"
                                    PopupClass="giz-scrollbar--v" />
                    </div>
                }
            }
            @if (UserRegistrationViewState.DefaultUserGroupRequiredInfo.Address)
            {
                <div class="giz-registration-form-input">
                    <TextInput Value="@ViewState.Address"
                               ValueChanged="@((string value) => UserRegistrationAdditionalFieldsService.SetAddress(value))"
                               ValueExpression="@(() => ViewState.Address)"
                               Size="InputSizes.Small"
                               IsFullWidth="true"
                               Label="@LocalizationService.GetString("GIZ_USER_ADDRESS")" />
                </div>
            }
            @if (UserRegistrationViewState.DefaultUserGroupRequiredInfo.PostCode)
            {
                <div class="giz-registration-form-input">
                    <TextInput Value="@ViewState.PostCode"
                               ValueChanged="@((string value) => UserRegistrationAdditionalFieldsService.SetPostCode(value))"
                               ValueExpression="@(() => ViewState.PostCode)"
                               Size="InputSizes.Small"
                               IsFullWidth="true"
                               Label="@LocalizationService.GetString("GIZ_USER_POST_CODE")" />
                </div>
            }
            @if (UserRegistrationViewState.DefaultUserGroupRequiredInfo.Mobile)
            {
                @if (UserRegistrationViewState.ConfirmationMethod != RegistrationVerificationMethod.MobilePhone)
                {
                    <div class="giz-registration-form-input">
                        <MaskedPhoneInput Mask="###-###-####"
                                          Prefix="@ViewState.Prefix"
                                          AllowMoreDigits="@(string.IsNullOrEmpty(ViewState.Prefix) || ViewState.Prefix == "+")"
                                          Value="@ViewState.MobilePhone"
                                          ValueChanged="@((string value) => UserRegistrationAdditionalFieldsService.SetMobilePhone(value))"
                                          ValueExpression="@(() => ViewState.MobilePhone)"
                                          Size="InputSizes.Small"
                                          IsFullWidth="true"
                                          Label="@LocalizationService.GetString("GIZ_GEN_MOBILE_PHONE")" />
                    </div>
                }
            }
            <div>
                <Button IsDisabled="@(ViewState.IsValid == false || ViewState.IsLoading)"
                        Color="ButtonColors.Accent"
                        Size="ButtonSizes.Large"
                        IsFullWidth="true"
                        @onclick="@UserRegistrationAdditionalFieldsService.SubmitAsync">
                    <ChildContent>
                        @if (ViewState.IsLoading)
                        {
                            <Spinner />
                        }
                        else
                        {
                            <div>@LocalizationService.GetString("GIZ_REGISTRATION_SIGN_UP_BUTTON")</div>
                        }
                    </ChildContent>
                </Button>
            </div>
        </EditForm>
    </CardBody>
    <CardFooter>
        <InputLanguageMenu />
    </CardFooter>
</LoginCard>