﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase

<HostedDialog>
    <div class="giz-client-dialog giz-user-agreement-dialog">
        <div class="giz-client-dialog__header">
            <div class="giz-client-dialog__header__title">@LocalizationService.GetString("GIZ_USER_AGREEMENT_DIALOG_TITLE")</div>
            <div class="giz-client-dialog__header__subtitle">@Name</div>
            <IconButton SVGIcon="Icons.Close" Variant="ButtonVariants.Text" Size="ButtonSizes.Small" Class="close-btn" @onclick="CloseDialogAsync" />
        </div>
        <div class="giz-client-dialog__body giz-scrollbar--v">
            <div class="user-agreement">@Agreement</div>
        </div>
        <div class="giz-client-dialog__footer">
            <div class="giz-user-agreement-checkboxes">
                <CheckBox Label="I have read and agree to the terms of service."
                          IsChecked="@_accepted"
                          IsCheckedChanged="@((value) => { _accepted = value; } )"
                          Class="@(IsRejectable ? "" : "giz-user-agreement-checkbox--required")" />
            </div>
            <Button IsDisabled="@(!IsRejectable && !_accepted)"
                    Color="ButtonColors.Accent"
                    Size="ButtonSizes.ExtraLarge"
                    IsFullWidth="true"
                    @onclick="ContinueAsync">
                @LocalizationService.GetString("GIZ_GEN_CONTINUE")
            </Button>
        </div>
    </div>
</HostedDialog>
