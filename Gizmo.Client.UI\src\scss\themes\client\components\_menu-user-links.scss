//=============== Menu User Menu ================//

.giz-user-links {
    display: grid;
    grid-auto-flow: row;
    gap: 1.6rem;
    padding: 1.6rem;
    background-color: #22272B;
    border-radius: 0 0 0.8rem 0.8rem;
    box-shadow: 0px 32px 64px rgba(0, 0, 0, 0.37), 0px 2px 21px rgba(0, 0, 0, 0.37);
    max-height: 100%;
    overflow: hidden;
    min-width: 36.8rem;
}

.giz-user-links-item {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    color: #E0E1E6;
    cursor: pointer;
    //Font ?
    font-size: 1.5rem;
    line-height: 2.0rem;
    font-weight: 500;
    transition: color 0.2s;

    &:hover {
        color: #0F7CFE;

        .giz-user-links-item__icon {
            background-color: #1D334E;

            svg {
                color: #0F7CFE;
            }
        }
    }

    &__icon {
        width: 3.6rem;
        height: 3.6rem;
        background-color: #373839;
        box-shadow: inset 0 0 10.1rem rgba(255, 255, 255, 0.06);
        backdrop-filter: blur(6.0rem);
        border-radius: 0.8rem;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: background-color 0.2s;

        svg {
            color: #E0E1E6;
            transition: color 0.2s;
        }
    }
}
