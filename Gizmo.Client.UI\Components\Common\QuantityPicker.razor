﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase

<div class="@Class @ClassName" @onclick="OnClickHandler">
    <Button IsDisabled="@(Quantity == Minimum)" Variant="ButtonVariants.Text" Size="@(Size == ButtonSizes.Small ? ButtonSizes.ExtraSmall : ButtonSizes.Small)" Text="-" Class="giz-decrease-btn" @onclick="RemoveQuantity" />
    <span>@Quantity</span>
    <Button Variant="ButtonVariants.Text" Size="@(Size == ButtonSizes.Small ? ButtonSizes.ExtraSmall : ButtonSizes.Small)" Text="+" Class="giz-increase-btn" @onclick="AddQuantity" />
</div>
