//=============== Select ================//

.giz-select {
    [#{$client-theme-comp-attr}] & {
        &.open {
            .giz-input__icon-right {
                transform: rotate(180deg);
            }
        }

        .giz-select__dropdown {
            background-color: #22272B;
            box-shadow: 0 0.2rem 1.6rem rgba(0, 0, 0, 0.25);
            border-radius: 0.8rem;
            padding: 0.8rem;
            border: unset;

            .giz-list {
                padding: unset;
                //background-color: #22272B;

                .giz-list-item {
                    padding: 0.9rem 1.6rem;
                    color: #E4E6EB;
                    border-radius: 0.8rem;
                    @include font-m-theme-client($font-weight-light-theme-client);
                }

                &.giz-list--selectable {
                    .giz-list-item {
                        &.selected {
                            //Default Theme
                            background-color: unset;
                            color: #0F9FFF;
                        }

                        &.active {
                            //Default Theme
                            background-color: #373839;
                            color: #E4E6EB;
                        }
                    }
                }

                &.giz-list--clickable {
                    .giz-list-item {
                        &:hover {
                            //Default Theme
                            background-color: rgba(250, 250, 250, 0.23);
                        }
                    }
                }
            }
        }
    }
}