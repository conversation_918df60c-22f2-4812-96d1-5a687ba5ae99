﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase

<div class="giz-app-card">
    <div class="giz-app-card__content">
        <div class="giz-app-card__content__image">
            <div class="giz-app-card__content__image__hovered">
                <div class="giz-app-card__content__image__hovered__header">

                    <div class="giz-app-card__content--hovered giz-scrollbar-slim--v" @onclick="@Ignore">
                        <ApplicationCardHover @key="Application.ApplicationId" ApplicationId="@Application.ApplicationId" />
                    </div>

                </div>
            </div>

            <GizImage @key="Application.ApplicationId" ImageType="Gizmo.UI.ImageType.Application" ImageId="@Application.ImageId" ImageFitType="ImageFitType.Fill">
                <EmptyResultPlaceholder>
                    <div class="giz-default-image">
                        <img src="_content/Gizmo.Client.UI/img/no-app-image.svg" alt='loading' />
                    </div>
                </EmptyResultPlaceholder>
            </GizImage>

        </div>
        <div class="giz-app-card__content__details">
            <div class="giz-app-card__title">
                @Application?.Title
            </div>
        </div>
        <div class="giz-app-card__content__footer">
            <div class="giz-app-card-text">
                @if (Application != null)
                {
                    <ApplicationCardCategory @key="Application.ApplicationCategoryId" ApplicationCategoryId="@Application.ApplicationCategoryId" />
                }
            </div>
            @if (!AppDetailsPageViewState.DisableAppDetails)
            {
                <IconButton Variant="ButtonVariants.Text" SVGIcon="Icons.Open_Client" Size="ButtonSizes.Small" @onclick="OpenDetails" />
            }
        </div>
    </div>
</div>
