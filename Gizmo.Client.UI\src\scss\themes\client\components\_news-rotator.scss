//=============== News Rotator ================//

.giz-news-rotator {
    position: relative;
    width: 100%;
    height: 4.8rem;
    overflow: hidden;

    &-wrapper {
        height: 100%;
        padding: 1.6rem;
    }

    &-item {
        display: grid;
        grid-template-columns: min-content 1fr;
        align-items: center;
        gap: 1.0rem;
        visibility: hidden;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        cursor: pointer;

        &__image {
            position: relative;
            width: 4.8rem;
            height: 4.8rem;

            &__feed {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border-radius: 0.9rem;
                overflow: hidden;

                & > img {
                    width: 100%;
                    height: 100%;
                }
            }

            &__channel {
                position: absolute;
                bottom: 0;
                right: -0.6rem;
                width: 2rem;
                height: 2rem;
                z-index: 1;

                & > img {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        &__message {
            width: 100%;
            max-height: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            @include font-m-theme-client($font-weight-light-theme-client);
        }

        &--current {
            visibility: visible;
        }

        &--next {
            visibility: visible;
        }

        &.fade-out {
            left: -100%;
            animation-duration: 1s;
            animation-name: rotator-fade-out-current-anim;
        }

        &.fade-in {
            animation-duration: 1s;
            animation-name: rotator-fade-in-next-anim;
        }
    }
}

@keyframes rotator-fade-out-current-anim {
    from {
        left: 0;
        opacity: 1;
    }

    to {
        left: -100%;
        opacity: 0;
    }
}

@keyframes rotator-fade-in-next-anim {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}