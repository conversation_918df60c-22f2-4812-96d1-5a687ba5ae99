﻿@namespace Gizmo.Client.UI.Pages
@inherits CustomDOMComponentBase
@using Gizmo.Client.UI.View.States
@using System.Globalization

<div class="giz-profile">
    <div class="giz-profile__body">
        <ProfileHeader />
    
        <ProfileNavigation />
        
        <div class="giz-profile-user-purchases">
            <div class="giz-profile-user-purchases__header">
                Available time
            </div>

            <div class="giz-table-history">
                <DataGrid ItemSource="@ViewState.DepositTransactions.ToList()"
                          Context="depositTranscations"
                          HasStickyHeader="true"
                          Class="giz-scrollbar--v">
                    <ChildContent>
                        <DataGridColumn Field="@(nameof(DepositTransactionViewState.TransactionDate))" TItemType="DepositTransactionViewState" Context="depositTranscation">
                            <HeaderTemplate>
                                Time
                            </HeaderTemplate>
                            <CellTemplate>
                                @depositTranscation.TransactionDate.ToString()
                            </CellTemplate>
                        </DataGridColumn>
                        <DataGridColumn Field="@(nameof(DepositTransactionViewState.DepositTransactionType))" TItemType="DepositTransactionViewState" Context="depositTranscation">
                            <HeaderTemplate>
                                Type
                            </HeaderTemplate>
                            <CellTemplate>
                                @depositTranscation.DepositTransactionType
                            </CellTemplate>
                        </DataGridColumn>
                        <DataGridColumn Field="@(nameof(DepositTransactionViewState.Amount))" TItemType="DepositTransactionViewState" Context="depositTranscation">
                            <HeaderTemplate>
                                Amount
                            </HeaderTemplate>
                            <CellTemplate>
                                <div class="@(depositTranscation.DepositTransactionType == DepositTransactionType.Deposit ? "purchase-details-deposit" : depositTranscation.DepositTransactionType == DepositTransactionType.Withdraw ? "purchase-details-withdraw" : "")">
                                    @depositTranscation.Amount.ToString("C", CultureInfo.CurrentCulture)
                                </div>
                            </CellTemplate>
                        </DataGridColumn>
                        <DataGridColumn Field="@(nameof(DepositTransactionViewState.Amount))" TItemType="DepositTransactionViewState" Context="depositTranscation">
                            <HeaderTemplate>
                                Balance
                            </HeaderTemplate>
                            <CellTemplate>
                                @depositTranscation.Amount.ToString("C", CultureInfo.CurrentCulture)
                            </CellTemplate>
                        </DataGridColumn>
                        <DataGridColumn Field="@(nameof(DepositTransactionViewState.Amount))" TItemType="DepositTransactionViewState" Context="depositTranscation">
                            <HeaderTemplate>
                                Payment method
                            </HeaderTemplate>
                            <CellTemplate>
                                @depositTranscation.Amount
                            </CellTemplate>
                        </DataGridColumn>
                    </ChildContent>
                </DataGrid>
            </div>
        </div>

    </div>
</div>
