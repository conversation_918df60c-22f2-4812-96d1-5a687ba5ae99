﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase

<div class="giz-profile-navigation">
    
    <div class="giz-profile-navigation-item">
        <NavLink href="@ClientRoutes.UserProfileRoute" Match="NavLinkMatch.All">
            <Icon SVGIcon="Icons.User_Client" />
            @LocalizationService.GetString("GIZ_USER_DETAILS")
        </NavLink>
    </div>

    @*<div class="giz-profile-navigation-item">
        <NavLink href="@ClientRoutes.UserProductsRoute" Match="NavLinkMatch.All">
            <Icon SVGIcon="Icons.WorkHistory_Client" />
            @LocalizationService.GetString("GIZ_USER_AVAILABLE_TIME")
        </NavLink>
    </div>*@

    <div class="giz-profile-navigation-item">
        <NavLink href="@ClientRoutes.UserPurchasesRoute" Match="NavLinkMatch.All">
            <Icon SVGIcon="Icons.ShoppingCart_Client" />
            @LocalizationService.GetString("GIZ_USER_PURCHASES")
        </NavLink>
    </div>

    @*<div class="giz-profile-navigation-item">
        <NavLink href="@ClientRoutes.UserDepositsRoute" Match="NavLinkMatch.All">
            <Icon SVGIcon="Icons.WiFi_Client" />
            @LocalizationService.GetString("GIZ_USER_TRANSACTIONS")
        </NavLink>
    </div>*@
    
</div>
