//=============== Page Profile ================//

.giz-profile {
    height: 100%;
    overflow: auto;

    .giz-user-avatar {
        width: 5.6rem;
        height: 5.6rem;
    }

    &-sections {
        width: 61.3rem;
    }

    &-section {
        margin-top: 4rem;

        &__header {
            margin-bottom: 1.2rem;
            //Font
            font-family: 'Rubik';
            font-style: normal;
            font-weight: 400;
            font-size: 24px;
            line-height: 30px;
        }

        &-item {
            display: grid;
            grid-template-columns: min-content 1fr min-content;
            align-items: center;
            gap: 1.6rem;
            padding-top: 1.2rem;
            padding-bottom: 1.2rem;

            &__icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 4.4rem;
                height: 4.4rem;
                background-color: #22272B;
                border-radius: 1.6rem;
            }

            &__info {
                &__title {
                    color: rgba(255, 255, 255, 0.6);
                    @include font-s-theme-client($font-weight-light-theme-client);
                }

                &__text {
                    @include font-l-theme-client($font-weight-light-theme-client);

                    &--name {
                        color: rgba(255, 255, 255, 0.6);
                        @include font-m-theme-client($font-weight-light-theme-client);
                    }
                }
            }

            .giz-button {
                width: 3.6rem;
                height: 3.6rem;
            }
        }
    }

    &__body {
        display: grid;
        grid-template-rows: min-content min-content 1fr;
        min-height: 100%;
        width: 113.8rem;
        overflow: hidden;

        &-wrapper {
            display: flex;
            justify-content: center;
            padding: 7.2rem 0 3rem 0;
        }
    }

    &-navigation {
        display: flex;
        gap: 6.2rem;
        padding: 2.4rem 3.2rem;
        border: 0.1rem solid rgba(246, 251, 253, 0.06);
        border-radius: 1.6rem;
        white-space: nowrap;

        &-item {
            height: 4.6rem;
            @include font-l-theme-client($font-weight-regular-theme-client);

            .giz-icon--medium {
                width: 2.0rem;
                height: 2.0rem;
            }

            & > a {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                color: white;

                .giz-icon {
                    color: #FAFAFA;
                    margin-right: 1.5rem;
                }

                &.active {
                    color: $primary-color-theme-client;
                    position: relative;

                    svg {
                        color: #0078D2;
                    }

                    &::before {
                        content: '';
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        width: 100%;
                        height: 0.3rem;
                        background-color: $primary-color-theme-client;
                        border-radius: 0.2rem;
                    }
                }

                & > span {
                    display: none;
                }
            }
        }
    }

    &-user-details {
        margin-top: 4.0rem;

        &__header {
            color: #FAFAFA;
            margin-bottom: 2.4rem;
            @include font-h5-theme-client($font-weight-regular-theme-client);

            .giz-icon {
                margin-left: 1.8rem;
            }
        }
    }

    &-user-purchases {
        display: grid;
        grid-template-rows: min-content 1fr;
        padding: 4.0rem 0 0 0;
        height: 100%;
        //max-height: 65rem;
        overflow: hidden;

        &__header {
            color: #FAFAFA;
            margin-bottom: 2.4rem;
            @include font-h5-theme-client($font-weight-regular-theme-client);
        }

        &__body {
            height: 100%;
            overflow: hidden;
            background-color: #191c1d;
            border: 0.1rem solid #27292a;
            border-radius: 8px;
            white-space: nowrap;

            .giz-data-grid {
                [#{$client-theme-comp-attr}] & {
                    background-color: #191c1d;

                    & > thead td, & > thead th {
                        background-color: #191c1d;
                    }

                    & > tbody > tr:not(.giz-data-grid-row-detail) {
                        border-top: 0.1rem solid #191c1d;

                        &:hover {
                            background-color: #191c1d;
                        }

                        &:last-child {
                            border-bottom: 0.1rem solid #191c1d;
                        }
                    }
                }
            }

            table {
                td:first-child {
                    width: 100%;
                }

                td:last-child {
                    min-width: 15rem;
                }
            }

            svg {
                width: 1.4rem;
                height: 1.4rem;
            }
        }

        &__footer {
            display: flex;
            justify-content: flex-end;
            padding-top: 3.2rem;

            .giz-button {
                width: 4.4rem;
                height: 4.4rem;

                &:hover {
                    background-color: rgba(250, 250, 250, 0.23);
                    color: #fff;
                }
            }
        }
    }
}

.giz-profile-header {
    display: flex;
    align-items: center;
    gap: 1.6rem;
    margin-bottom: 4.8rem;

    .giz-user-avatar {
        width: 11.2rem;
        height: 11.2rem;
    }

    &__info {
        display: grid;
        grid-template-rows: min-content min-content;
        gap: 1.6rem;

        &__username {
            color: #FAFAFA;
            text-shadow: 0 0.4rem 0.4rem rgba(0, 0, 0, 0.25), 0 0.4rem 0.8rem rgba(0, 0, 0, 0.2);
            //Font
            font-weight: 500;
            font-size: 24px;
            line-height: 30px;
        }

        &__stats {
            display: flex;
            gap: 1.6rem;

            &-item {
                display: flex;
                align-items: center;
                gap: 0.8rem;
                padding-right: 1.6rem;

                &:last-child {
                    border-right: none;
                }

                .giz-icon {
                    margin-right: 1.0rem;
                }
            }

            &-text {

                .giz-title {
                    color: rgba(255, 255, 255, 0.6);
                    text-shadow: 0 0.2rem 0.2rem rgba(0, 0, 0, 0.2), 0 0.1rem 0.1rem rgba(0, 0, 0, 0.2);
                    @include font-s-theme-client($font-weight-light-theme-client);
                }

                .giz-numbers {
                    color: #FAFAFA;
                    text-shadow: 0 0.2rem 0.2rem rgba(0, 0, 0, 0.2), 0 0.1rem 0.1rem rgba(0, 0, 0, 0.2);
                    @include font-xxl-theme-client($font-weight-regular-theme-client);
                }
            }
        }
    }
}

.order-invoice-unpaid {
    background-color: #FFD79D;
    color: #202223;
    border-radius: 0.4rem;
    padding: 0.3rem 0.8rem;
    text-align: center;
    @include font-s-theme-client($font-weight-light-theme-client);
}

.order-invoice-partialy-paid {
    background-color: #FFD79D;
    color: #202223;
    border-radius: 0.4rem;
    padding: 0.3rem 0.8rem;
    text-align: center;
    @include font-s-theme-client($font-weight-light-theme-client);
}

.order-invoice-paid {
    background-color: #E4E5E7;
    color: #202223;
    border-radius: 0.4rem;
    padding: 0.3rem 0.8rem;
    text-align: center;
    @include font-s-theme-client($font-weight-light-theme-client);
}

.order-on-hold {
    background-color: #A4E8F2;
    color: #202223;
    border-radius: 0.4rem;
    padding: 0.3rem 0.8rem;
    text-align: center;
    @include font-s-theme-client($font-weight-light-theme-client);
}

.order-completed {
    background-color: #E4E5E7;
    color: #202223;
    border-radius: 0.4rem;
    padding: 0.3rem 0.8rem;
    text-align: center;
    @include font-s-theme-client($font-weight-light-theme-client);
}

.order-canceled {
    background-color: #FED3D1;
    color: #202223;
    border-radius: 0.4rem;
    padding: 0.3rem 0.8rem;
    text-align: center;
    @include font-s-theme-client($font-weight-light-theme-client);
}

.order-accepted {
    background-color: #AEE9D1;
    color: #202223;
    border-radius: 0.4rem;
    padding: 0.3rem 0.8rem;
    text-align: center;
    @include font-s-theme-client($font-weight-light-theme-client);
}

.order-line-details {
    display: flex;
    flex-direction: column;
    @include font-s-theme-client($font-weight-light-theme-client);

    a.order-line-details-product-name {
        color: #57BCFF;
    }

    &-product-name {
        height: 2.2rem;
        @include font-m-theme-client($font-weight-light-theme-client);
    }

    &-quantity {
        height: 2.2rem;
    }

    &-price {
        height: 2.2rem;
        text-align: right;
    }
}

.order-details {
    display: grid;
    grid-template-columns: min-content 1fr;
    //grid-template-rows: 2.2rem;
    grid-gap: 1.6rem;
    color: #FAFAFA;
    @include font-s-theme-client($font-weight-light-theme-client);

    &-points {
        height: 2.2rem;
        white-space: normal;

        &-title {
            color: rgba(255, 255, 255, 0.6);
        }
    }

    &-notes {
        white-space: normal;

        &-title {
            color: rgba(255, 255, 255, 0.6);
        }
    }
}

.purchase-details-deposit {
    color: #24C38E;
}

.purchase-details-withdraw {
    color: #FF4443;
}
