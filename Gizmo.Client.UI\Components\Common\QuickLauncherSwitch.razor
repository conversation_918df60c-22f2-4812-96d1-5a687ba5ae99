﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase

<ButtonGroup Class="quick-launcher-switch" SelectedItemsChanged="@SelectTab">
    <Button Name="QuickLaunch" IsSelected="@(SelectedTabIndex == 0)">
        <Icon Size="IconSizes.Small" SVGIcon="Icons.Rocket_Client" />
    </Button>
    <Button Name="Recent" IsSelected="@(SelectedTabIndex == 1)">
        <Icon Size="IconSizes.Small" SVGIcon="Icons.History_Client" />
    </Button>
</ButtonGroup>