﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

	<PropertyGroup>
		<OutputType>Library</OutputType>
		<TargetFramework>net6.0</TargetFramework>
		<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
    <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
	</PropertyGroup>
  
	<ItemGroup>
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="7.0.5" />
		<ProjectReference Include="..\Submodules\Gizmo.Client.UI.Services\Gizmo.Client.UI.Services\Gizmo.Client.UI.Services.csproj" />
		<ProjectReference Include="..\Submodules\Gizmo.Web.Components\Gizmo.Web.Components.csproj" />
	</ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="wwwroot\notifications.html"></EmbeddedResource>
  </ItemGroup>

  <Target Name="PreBuild" BeforeTargets="PreBuildEvent">
    <Exec Command="npm install" WorkingDirectory="." />
    <Exec Condition="'$(Configuration)' == 'Debug'" Command="npm run build_dev" WorkingDirectory="." />
    <Exec Condition="'$(Configuration)' == 'Release'" Command="npm run build_prod" WorkingDirectory="." />
  </Target>
  
</Project>
