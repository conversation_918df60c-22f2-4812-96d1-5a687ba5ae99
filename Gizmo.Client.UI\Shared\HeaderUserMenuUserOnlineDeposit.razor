﻿@namespace Gizmo.Client.UI
@inherits CustomDOMComponentBase
@using System.Globalization

<div class="giz-header__user-menu-item giz-user-online-deposit-dropdown @(IsOpen ? "open" : "")">
    <button class="user-menu-item-button--box" @onclick="(args) => OnClick.InvokeAsync(args)" @onclick:stopPropagation="true">
        <Icon SVGIcon="Icons.Deposit_Client" />
    </button>
    <MenuUserOnlineDepositContainer @bind-IsOpen="IsOpen" />
</div>