﻿@namespace Gizmo.Client.UI.Components

<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="giz-points-icon">
<g filter="url(#@FilterId)">
<path d="M10.0004 15.9999C14.1732 15.9999 17.5559 12.6172 17.5559 8.44438C17.5559 4.27156 14.1732 0.888824 10.0004 0.888824C5.82756 0.888824 2.44482 4.27156 2.44482 8.44438C2.44482 12.6172 5.82756 15.9999 10.0004 15.9999Z" fill="url(#@Linear0Id)" />
<path d="M10.0004 15.1111C14.1732 15.1111 17.5559 11.7284 17.5559 7.55556C17.5559 3.38274 14.1732 0 10.0004 0C5.82756 0 2.44482 3.38274 2.44482 7.55556C2.44482 11.7284 5.82756 15.1111 10.0004 15.1111Z" fill="#FFCC4D"/>
<path d="M10.0001 14.2218C13.4365 14.2218 16.2223 11.436 16.2223 7.99957C16.2223 4.56313 13.4365 1.77734 10.0001 1.77734C6.56362 1.77734 3.77783 4.56313 3.77783 7.99957C3.77783 11.436 6.56362 14.2218 10.0001 14.2218Z" fill="#FFE8B6"/>
<path d="M9.99957 13.7773C13.436 13.7773 16.2218 10.9915 16.2218 7.55505C16.2218 4.11861 13.436 1.33282 9.99957 1.33282C6.56313 1.33282 3.77734 4.11861 3.77734 7.55505C3.77734 10.9915 6.56313 13.7773 9.99957 13.7773Z" fill="url(#@Linear1Id)"/>
</g>
<defs>
<filter id="@FilterId" x="0" y="0" width="20" height="20" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2137_82988"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_2137_82988" result="effect2_dropShadow_2137_82988"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_2137_82988" result="shape"/>
</filter>
<linearGradient id="@Linear0Id" x1="2.44482" y1="8.44438" x2="17.5559" y2="8.44438" gradientUnits="userSpaceOnUse">
<stop stop-color="#E65C00"/>
<stop offset="1" stop-color="#F9D423"/>
</linearGradient>
<linearGradient id="@Linear1Id" x1="3.77734" y1="7.55505" x2="16.2218" y2="7.55505" gradientUnits="userSpaceOnUse">
<stop stop-color="#F09819"/>
<stop offset="1" stop-color="#EDDE5D"/>
</linearGradient>
</defs>
</svg>

@code
{
    public string FilterId { get; set; } = ComponentIdGenerator.Generate();
    public string Linear0Id { get; set; } = ComponentIdGenerator.Generate();
    public string Linear1Id { get; set; } = ComponentIdGenerator.Generate();
}
