﻿@inherits CustomDOMComponentBase
@using Microsoft.AspNetCore.Components.Forms

@if (ViewState.IsLocking)
{
    <EditForm EditContext="@UserLockService.EditContext">
        <div class="giz-user-lock-overlay open">
            <div class="giz-user-lock-wrapper">
                <div class="giz-user-lock">
                    <div class="giz-user-lock__title">@LocalizationService.GetString("GIZ_USER_LOCK_SCREEN_TITLE")</div>
                    <div class="giz-user-lock__subtitle">@LocalizationService.GetString("GIZ_USER_LOCK_SCREEN_SUBTITLE")</div>
                    <div class="giz-user-lock__code">
                        <TextInput IsTransparent="true"
                                   Value="@ViewState.InputPassword"
                                   ValueChanged="@((string value) => UserLockService.SetInputPassword(value))"
                                   ValueExpression="@(() => ViewState.InputPassword)"
                                   ValidationErrorStyle="ValidationErrorStyles.BorderOnly"
                                   UpdateOnInput="true"
                                   MaxLength="4" />
                    </div>
                    <div class="giz-user-lock__numpad">
                        @for (int i = 0; i <= 9; i++)
                        {
                            int local_i = i;
                            <Button Color="ButtonColors.Primary" Size="ButtonSizes.ExtraLarge" @onclick="@(() => UserLockService.PutDigitAsync(local_i))">@local_i</Button>
                        }
                        <Button Color="ButtonColors.Primary" Size="ButtonSizes.ExtraLarge" @onclick="@(() => UserLockService.DeleteDigitAsync())" LeftSVGIcon="Icons.Backspace"></Button>
                    </div>
                    <div class="giz-user-lock__actions">
                        <Button Color="ButtonColors.Accent" Size="ButtonSizes.ExtraLarge" @onclick="@(() => UserLockService.SetPasswordAsync())">@LocalizationService.GetString("GIZ_GEN_SAVE")</Button>
                        <Button Color="ButtonColors.Accent" Variant="ButtonVariants.Outline" Size="ButtonSizes.ExtraLarge" @onclick="@(() => UserLockService.CancelLockAsync())">@LocalizationService.GetString("GIZ_GEN_CANCEL")</Button>
                    </div>
                    <div class="giz-user-lock__note"></div>
                </div>
            </div>
        </div>
    </EditForm>
}
else
{
    @if (ViewState.IsLocked)
    {
        <EditForm EditContext="@UserLockService.EditContext">
            <div class="giz-user-lock-overlay open">
                <div class="giz-user-lock-wrapper">
                    <div class="giz-user-lock @(!string.IsNullOrEmpty(ViewState.Error) ? "error" : "")">
                        <div class="giz-user-lock__title">@LocalizationService.GetString("GIZ_USER_LOCK_SCREEN_LOCKED_TITLE")</div>
                        <div class="giz-user-lock__subtitle">@LocalizationService.GetString("GIZ_USER_LOCK_SCREEN_LOCKED_SUBTITLE")</div>
                        <div class="giz-user-lock__code">
                            <TextInput IsTransparent="true"
                                       Value="@ViewState.InputPassword"
                                       ValueChanged="@((string value) => UserLockService.SetInputPassword(value))"
                                       ValueExpression="@(() => ViewState.InputPassword)"
                                       ValidationErrorStyle="ValidationErrorStyles.BorderOnly"
                                       UpdateOnInput="true"
                                       MaxLength="4" />
                        </div>
                        <div class="giz-user-lock__error">@ViewState.Error</div>
                        <div class="giz-user-lock__numpad">
                            @for (int i = 0; i <= 9; i++)
                            {
                                int local_i = i;
                                <Button Color="ButtonColors.Primary" Size="ButtonSizes.ExtraLarge" @onclick="@(() => UserLockService.PutDigitAsync(local_i))">@local_i</Button>
                            }
                            <Button Color="ButtonColors.Primary" Size="ButtonSizes.ExtraLarge" @onclick="@(() => UserLockService.DeleteDigitAsync())" LeftSVGIcon="Icons.Backspace"></Button>
                        </div>
                        <div class="giz-user-lock__actions">
                            <Button Color="ButtonColors.Accent" Size="ButtonSizes.ExtraLarge" @onclick="@(() => UserLockService.UnlockAsync())">@LocalizationService.GetString("GIZ_USER_LOCK_SCREEN_ENTER_CODE")</Button>
                        </div>
                        <div class="giz-user-lock__note">@LocalizationService.GetString("GIZ_USER_LOCK_SCREEN_FORGOT_PIN")</div>
                    </div>
                </div>
            </div>
        </EditForm>
    }
}