//=============== Clien UI Login ================//

.giz-main-container {
    display: grid;
    grid-template-columns: 1fr min-content;
    height: 100vh;

    .giz-login {

        &__adv {
            display: grid;
            grid-template-rows: min-content 1fr min-content;
            position: relative;

            &__background {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 0;

                img {
                    width: 100%;
                    height: 100%;
                }
            }

            &__header {
                display: flex;
                justify-content: space-between;
                padding: 2.4rem 3.2rem;
                z-index: 1;

                .giz-nav-title {
                    //Font ?
                    font-size: 3.6rem;
                    line-height: 4.4rem;
                    font-weight: 700;
                }
            }

            &__body {
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1;

                .giz-carousel {
                    max-width: 69.2rem;
                    max-height: 51.6rem;
                }
            }

            &__footer {
                display: flex;
                align-items: center;
                padding: 0 0 2.6rem 4.6rem;
                z-index: 1;

                .giz-client-language-menu {
                    display: flex;
                    align-items: center;
                    margin-right: 1.5rem;

                    .giz-input-root {
                        width: unset;
                        border: 0.1rem solid transparent;
                        background-color: transparent;

                        &:hover {
                            border: 0.2rem solid rgba(246, 251, 253, 0.52);

                            &.giz-input-root--small {
                                padding: 0.9rem 1.2rem;
                            }
                        }

                        &:focus-within {
                            border: 0.2rem solid rgba(246, 251, 253, 0.52);

                            &.giz-input-root--small {
                                padding: 0.9rem 1.2rem;
                            }
                        }

                        .giz-input__icon-right {
                            display: none;
                        }
                    }

                    .giz-select__dropdown--full-width {
                        width: unset;
                    }
                }
            }
        }

        &__login {
            max-height: 100vh;
            //overflow-y: hidden;
            background-color: #0C0F11;
            position: relative;
        }
    }
}

.giz-server {
    display: flex;
    align-content: center;

    &__icon {
        align-self: center;
        margin-right: 0.7rem;

        &.disconnected {
            color: #F73B3B;
        }
    }

    .giz-version {
        &__title {
            color: #808185;
            @include font-s-theme-client($font-weight-light-theme-client);
        }

        &__version {
            color: white;
            @include font-s-theme-client($font-weight-light-theme-client);
        }
    }
}

.giz-login-card {
    display: grid;
    grid-template-rows: 1fr min-content 1fr;
    align-items: center;
    height: 100%;
    width: 74.0rem;
    min-width: 74.0rem;
    position: relative;
    /*overflow: hidden;*/
    padding: 1.0rem;

    &__header {
        display: grid;
        grid-template-rows: min-content 1fr;
        height: 100%;
        //align-items: start;
        width: 100%;
        overflow: hidden;

        .giz-login-new-user {
            margin-left: auto;
            margin-top: 1.0rem;
            text-align: right;
            @include font-m-theme-client($font-weight-light-theme-client);

            & > a {
                color: #3F8CFF;
                cursor: pointer;
            }
        }

        .giz-login-alerts {
            align-self: end;
            padding: 3.3rem 17.1rem;
            max-width: 100%;
            max-height: 100%;
            overflow: hidden;

            .giz-toast {
                max-width: 100%;
                max-height: 100%;
                overflow: hidden;

                &__body {
                    max-width: 100%;
                    max-height: 100%;
                    overflow: hidden;
                }
            }
        }
    }

    &__body {
        padding: 0 17.1rem;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
    }

    &__footer {
        display: grid;
        grid-template-rows: 1fr min-content;
        height: 100%;
        align-items: end;
    }
}

.giz-login-title {
    margin-bottom: 2.0rem;
    @include font-h5-theme-client($font-weight-bold-theme-client);
}

.giz-login-subtitle {
    margin-bottom: 2.4rem;
    @include font-l-theme-client($font-weight-light-theme-client);

    &--sign-up {
        margin-bottom: 2.4rem;
        color: rgba(255, 255, 255, 0.6);
        @include font-l-theme-client($font-weight-light-theme-client);
    }
}

.giz-login-forgot-password {
    position: absolute;
    top: 0;
    right: 0;
    white-space: nowrap;
    z-index: 1;

    & > a {
        position: absolute;
        right: 0;
        color: #57BCFF;
        @include font-m-theme-client($font-weight-light-theme-client);
    }
}

.giz-alternative-login {
    align-self: start;
    padding: 0 17.1rem;
    margin-top: 2.4rem;

    &__separator {
        color: white;
        position: relative;
        text-align: center;
        background-color: #0C0F11;
        margin-bottom: 2.4rem;
        @include font-l-theme-client($font-weight-light-theme-client);

        span {
            position: relative;
            background-color: #0C0F11;
            padding: 0 0.8rem;
            color: rgba(255, 255, 255, 0.6);
            @include font-m-theme-client($font-weight-light-theme-client);
        }

        &:before {
            content: '';
            border-bottom: 0.1rem solid rgba(246, 251, 253, 0.06);
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
        }
    }

    &__qr {
        display: grid;
        grid-template-columns: 1fr min-content;
        gap: 2.4rem;
        align-items: center;

        &-description {
            display: grid;
            grid-template-rows: auto auto;
            gap: 1.6rem;

            &__title {
                @include font-h5-theme-client($font-weight-bold-theme-client);
            }

            &__subtitle {
                @include font-l-theme-client($font-weight-light-theme-client);
            }
        }

        svg {
            width: 15.4rem;
            height: 15.4rem;
        }
    }
}

.giz-login-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(35, 35, 45, 0.9) 0%, rgba(42, 44, 53, 0.15) 100%);
    backdrop-filter: blur(3.0rem);
    z-index: 100;

    .giz-host-locked {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #FAFAFA;
        @include font-h1-theme-client($font-weight-bold-theme-client);

        &__message {
            display: flex;
            align-items: center;
            gap: 0.8rem;

            .giz-icon--large {
                width: 3.2rem;
                height: 3.2rem;
            }
        }
    }
}

.giz-password-tooltip {
    visibility: hidden;
    position: absolute;
    top: 50%;
    right: calc(100% + 1.2rem);
    transform: translate(0, calc(-50% + 1.7rem));
    padding: 1.6rem;
    background-color: #2B2D33;
    box-shadow: 0 0.4rem 2.0rem rgba(0, 0, 0, 0.08);
    border-radius: 0.8rem;
    display: grid;
    grid-template-columns: min-content max-content;
    gap: 1.6rem;

    &-message {
        &.passed {
            color: #219653;
        }
    }

    &__icon {
    }

    &__body {
        &__title {

            @include font-m-theme-client($font-weight-regular-theme-client);
        }

        &__messages {
            @include font-m-theme-client($font-weight-light-theme-client);
        }
    }

    &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 100%;
        margin-left: -0.5rem;
        border-width: 0.5rem;
        border-style: solid;
        border-color: #2B2D33;
        transform: translateY(-0.5rem) rotate(45deg);
    }

    &-root {
        position: relative;

        &:focus-within {
            .giz-password-tooltip {
                visibility: visible;
            }
        }
    }
}

.giz-login-method.giz-button-group, .giz-recovery-method.giz-button-group {
    display: flex;
    padding: 0.5rem;
    margin-bottom: 2.4rem;

    .giz-button {
        flex: 1;
        border-radius: 0.8rem;
        color: rgba(255, 255, 255, 0.8);
        //Font ?
        font-weight: 500;
        font-size: 1.6rem;
        line-height: 2.0rem;

        &.selected {
            background-color: #22272B;
        }
    }
}

.giz-login-adv {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-rows: 1fr min-content;

    &__image {
        background-color: #0C0F11;
        border-radius: 1.6rem;
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
        }
    }

    &__text {
        margin: 4.0rem 0 0 0;
        text-align: center;
        color: rgba(255, 255, 255, 0.6);
        text-shadow: 0 0.2rem 0.2rem rgba(0, 0, 0, 0.2), 0 0.1rem 0.1rem rgba(0, 0, 0, 0.2);
        @include font-m-theme-client($font-weight-light-theme-client);
    }
}

.giz-input-language-menu {
    justify-self: end;
    margin-bottom: 1.6rem;
    margin-right: 4.6rem;
    color: #d5d5d6;
    @include font-m-theme-client($font-weight-light-theme-client);

    &-item {
        display: flex;
        gap: 0.6rem;

        &__flag {
            display: flex;
            align-items: center;
        }
    }

    .giz-input-control {
        .giz-input-root {
            width: unset;
            border: 0.1rem solid transparent;

            &:hover {
                border: 0.2rem solid rgba(246, 251, 253, 0.52);

                &.giz-input-root--small {
                    padding: 0.9rem 1.2rem;
                }
            }

            &:focus-within {
                border: 0.2rem solid rgba(246, 251, 253, 0.52);

                &.giz-input-root--small {
                    padding: 0.9rem 1.2rem;
                }
            }

            .giz-input__icon-right {
                display: none;
            }
        }
    }

    .giz-select__dropdown--full-width {
        width: unset;
        left: unset;
        right: 0;
    }
}

////

.giz-reserve-pin-input {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;

    input {
        text-align: center;
    }
}

.giz-resend-button {
    position: relative;

    &__time {
        position: absolute;
        right: 2.4rem;
        top: 50%;
        transform: translate(0, -50%);
        @include font-s-theme-client($font-weight-light-theme-client);
    }
}

.giz-login-links {
    margin-left: 2.2rem;
    margin-top: 2.2rem;
    margin-right: 1.2rem;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
}

.giz-back-button {
    width: 4.2rem;
    height: 4.2rem;
    border: 0.1rem solid rgba(246, 251, 253, 0.28);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.giz-icon-button {
    [#{$client-theme-comp-attr}] & {
        &.giz-input-button-reveal {
            background-color: unset;
            width: 2.4rem;
            height: 2.4rem;
            min-width: 2.4rem;
            min-height: 2.4rem;

            .giz-icon {
                width: 2.4rem;
                height: 2.4rem;
                min-width: 2.4rem;
                min-height: 2.4rem;
            }
        }
    }
}