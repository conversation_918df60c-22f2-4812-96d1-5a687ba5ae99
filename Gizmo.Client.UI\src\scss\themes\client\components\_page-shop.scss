//=============== Page Shop ================//

.giz-shop {
    display: grid;
    grid-template-columns: 1fr min-content;
    height: 100%;
    overflow: hidden;
    flex-grow: 1;
    max-width: 2560px;

    .giz-ads-carousel {
        &-item {
            &.previous-out {
                mask-image: linear-gradient( to left, rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.0) );
                -webkit-mask-image: linear-gradient( to left, rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.0) );
            }

            &.previous {
                mask-image: linear-gradient( to left, rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.0) );
                -webkit-mask-image: linear-gradient( to left, rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.0) );
            }

            &.next {
                mask-image: linear-gradient( to right, rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.0) );
                -webkit-mask-image: linear-gradient( to right, rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.0) );
            }

            &.next-out {
                mask-image: linear-gradient( to right, rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.0) );
                -webkit-mask-image: linear-gradient( to right, rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.0) );
            }
        }
    }

    &-wrapper {
        display: flex;
        justify-content: center;
        height: 100%;
    }

    &__products {
        display: grid;
        grid-template-rows: min-content 1fr;
        height: 100%;
        overflow: hidden;

        &__header {
            width: 100%;
            overflow: auto;
            padding: 1.6rem;

            &__tab {
                background-color: rgba(255, 255, 255, 0.01);
                box-shadow: inset 0 0 10.1rem rgba(255, 255, 255, 0.06);
                backdrop-filter: blur(6.0rem);
                border-radius: 1.6rem;
                height: 6.6rem;
                padding: 1.6rem;
                display: flex;
                gap: 1.0rem;
                width: 100%;
                overflow: hidden;
            }
        }

        &__body {
            padding: 1.6rem;
            height: 100%;
            overflow: auto;

            &__ads {
            }

            .giz-section {
                margin-top: 1.6rem;
            }
        }

        &__list {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            grid-auto-rows: minmax(42.0rem, 1fr);
            grid-column-gap: 1.6rem;
            grid-row-gap: 1.6rem;
            justify-content: left;
            height: 100%;
            overflow: auto;
        }
    }

    &__order {
        height: 100%;
        overflow: hidden;
    }

    .giz-ads-carousel-item__content__text {
        max-width: 28.8rem;
    }
}
