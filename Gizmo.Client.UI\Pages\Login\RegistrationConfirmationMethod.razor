﻿@namespace Gizmo.Client.UI.Pages
@inherits CustomDOMComponentBase
@layout _Layout_Login
@using System.Threading;
@using Gizmo.UI;
@using Microsoft.AspNetCore.Components.Forms

<LoginCard>
    <CardHeader>
        <div class="giz-login-links">
            <div class="giz-login-new-user">@LocalizationService.GetString("GIZ_LOGIN_ALREADY_A_MEMBER") <a href="@ClientRoutes.LoginRoute">@LocalizationService.GetString("GIZ_REGISTRATION_SIGN_IN")</a></div>
        </div>
        <div class="giz-login-alerts">
            @if (ViewState.HasError)
            {
                <Toast CanClose="true" AlertType="AlertTypes.Danger" Text="@ViewState.ErrorMessage" />
            }
        </div>
    </CardHeader>
    <CardBody>
        <EditForm EditContext="@UserRegistrationConfirmationMethodService.EditContext">
            <div class="giz-login-title">@LocalizationService.GetString("GIZ_REGISTRATION_SIGN_UP_TITLE")</div>
            @if (UserRegistrationViewState.ConfirmationMethod == RegistrationVerificationMethod.Email)
            {
                <div class="giz-login-subtitle--sign-up">Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint. Velit officia consequat duis enim</div>
                <div class="giz-form-input">
                    <TextInput Value="@ViewState.Email"
                               ValueChanged="@((string value) => UserRegistrationConfirmationMethodService.SetEmail(value))"
                               ValueExpression="@(() => ViewState.Email)"
                               IsDisabled="@ViewState.IsLoading"
                               Size="InputSizes.Medium"
                               IsFullWidth="true"
                               Label="@LocalizationService.GetString("GIZ_GEN_EMAIL_ADDRESS")"/>
                </div>
            }
            @if (UserRegistrationViewState.ConfirmationMethod == RegistrationVerificationMethod.MobilePhone)
            {
                <div class="giz-login-subtitle--sign-up">@LocalizationService.GetString("GIZ_REGISTRATION_MOBILE_CONFIRMATION_MESSAGE")</div>
                <div class="giz-registration-form-input">
                    <IconSelect Placeholder="@LocalizationService.GetString("GIZ_GEN_SEARCH")"
                                IsVirtualized="false"
                                IsLoading="@(!_isLoaded)"
                                CanClearValue="@(!(GetSelectedCountry()?.PhonePrefix == "+"))"
                                OnClickClearValueButton="OnClickClearValueButtonHandler"
                                HandleSVGIcon="Icons.Select_Client"
                                TValue="IconSelectCountry"
                                ItemSource="@Countries"
                                SelectedItem="@GetSelectedCountry()"
                                SelectedItemChanged="@SetSelectedCountry"
                                IsDisabled="@ViewState.IsLoading"
                                Size="InputSizes.Medium"
                                IsFullWidth="true"
                                Label="@LocalizationService.GetString("GIZ_USER_COUNTRY")"
                                OpenDirection="PopupOpenDirections.Bottom"
                                MaximumHeight="20.0rem"
                                PopupClass="giz-scrollbar--v" />
                </div>
                <div class="giz-form-input">
                    <MaskedPhoneInput Mask="###-###-####"
                                      Prefix="@ViewState.Prefix"
                                      AllowMoreDigits="@(string.IsNullOrEmpty(ViewState.Prefix) || ViewState.Prefix == "+")"
                                      Value="@ViewState.MobilePhone"
                                      ValueChanged="@((string value) => UserRegistrationConfirmationMethodService.SetMobilePhone(value))"
                                      ValueExpression="@(() => ViewState.MobilePhone)"
                                      IsDisabled="@ViewState.IsLoading"
                                      Size="InputSizes.Medium"
                                      IsFullWidth="true"
                                      Label="@LocalizationService.GetString("GIZ_GEN_PHONE_NUMBER")" />
                </div>
            }
            <div>
                <Button IsDisabled="@(ViewState.IsValid == false || ViewState.IsLoading || UserVerificationViewState.IsVerificationLocked)"
                        Color="ButtonColors.Accent"
                        Size="ButtonSizes.ExtraLarge"
                        IsFullWidth="true"
                        @onclick="@(() => UserRegistrationConfirmationMethodService.SubmitAsync())">
                    <ChildContent>
                        @if (ViewState.IsLoading)
                        {
                            <Spinner />
                        }
                        else
                        {
                            <div>@LocalizationService.GetString("GIZ_USER_CONFIRMATION_SEND_CODE")</div>
                            @if (UserVerificationViewState.IsVerificationLocked)
                            {
                                <div class="giz-resend-button__time">@string.Format("{0:mm\\:ss}", UserVerificationViewState.Countdown)</div>
                            }
                        }
                    </ChildContent>
                </Button>
            </div>
        </EditForm>
    </CardBody>
    <CardFooter>
        <InputLanguageMenu />
    </CardFooter>
</LoginCard>
