[submodule "Submodules/Gizmo.Client.UI.Resources"]
	path = Submodules/Gizmo.Client.UI.Resources
	url = https://github.com/GAMP/Gizmo.Client.UI.Resources
	branch = dev
[submodule "Submodules/Gizmo.UI"]
	path = Submodules/Gizmo.UI
	url = https://github.com/GAMP/Gizmo.UI
	branch = dev
[submodule "Submodules/Gizmo.Web.Components"]
	path = Submodules/Gizmo.Web.Components
	url = https://github.com/GAMP/Gizmo.Web.Components
	branch = dev
[submodule "Submodules/Gizmo.Client.UI.Services"]
	path = Submodules/Gizmo.Client.UI.Services
	url = https://github.com/GAMP/Gizmo.Client.UI.Services
	branch = dev
[submodule "Submodules/Gizmo.Shared"]
	path = Submodules/Gizmo.Shared
	url = https://github.com/GAMP/Gizmo.Shared
	branch = dev
[submodule "Submodules/Gizmo.Client.Shared"]
	path = Submodules/Gizmo.Client.Shared
	url = https://github.com/GAMP/Gizmo.Client.Shared
	branch = dev
[submodule "Submodules/Gizmo.Web.Api.Models"]
	path = Submodules/Gizmo.Web.Api.Models
	url = https://github.com/GAMP/Gizmo.Web.Api.Models
	branch = dev