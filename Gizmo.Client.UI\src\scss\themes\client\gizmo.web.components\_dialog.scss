//=============== Dialog ================//

.giz-dialog {
    [#{$client-theme-comp-attr}] & {
        background-color: initial;
        background: linear-gradient(180deg, rgba(22, 30, 37, 0.8) 0%, rgba(11, 17, 23, 0.9) 100%);
        backdrop-filter: blur(1.5rem);
        z-index: 1001;
        //Scroll Issue
        display: block;
        padding: unset;

        &__content {
            &-wrapper {
                margin: 1rem;
                min-height: calc(100% - 2rem);
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }

        & > .giz-card {
            text-align: center;
            z-index: 1002;
            border: thin solid $unknown-color;
            background-color: $unknown-color;
            overflow: hidden;

            &::before {
                position: absolute;
                left: 0;
                bottom: 0;
                content: "";
                background-color: initial;
                background: linear-gradient(to bottom, rgba(90, 94, 107, 0) 0%, rgba(37, 140, 246, 0.45) 333.62%);
                height: 100%;
                width: 100%;
                z-index: -1;
            }
        }
    }
}