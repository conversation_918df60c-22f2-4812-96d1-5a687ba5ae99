﻿@namespace Gizmo.Client.UI.Components

<svg width="21" height="22" viewBox="0 0 21 22" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_dd_118_5829)">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.1243 5.33227C14.1243 7.05816 12.7252 8.45727 10.9993 8.45727C9.27344 8.45727 7.87433 7.05816 7.87433 5.33227C7.87433 3.60638 9.27344 2.20728 10.9993 2.20728C12.7252 2.20728 14.1243 3.60638 14.1243 5.33227ZM12.5499 17.1497C12.1206 17.5814 11.5571 17.7972 10.9941 17.7914C10.4312 17.7972 9.86773 17.5814 9.43839 17.1497L2.88464 10.5989C2.03064 9.7472 2.03064 8.35887 2.88464 7.50137C3.73806 6.6497 5.12464 6.6497 5.97864 7.50137C5.97864 7.50137 8.08098 9.6072 9.56381 11.0889C10.3536 11.8764 11.6346 11.8764 12.4245 11.0889C13.9073 9.6072 16.0096 7.50137 16.0096 7.50137C16.8636 6.6497 18.2502 6.6497 19.1036 7.50137C19.9576 8.35887 19.9576 9.7472 19.1036 10.5989L12.5499 17.1497Z" fill="#F2C94C" />
    </g>
    <defs>
        <filter id="filter0_dd_118_5829" x="-1" y="0" width="24" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
            <feOffset dy="1" />
            <feGaussianBlur stdDeviation="0.5" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0" />
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_118_5829" />
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
            <feOffset dy="2" />
            <feGaussianBlur stdDeviation="1" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0" />
            <feBlend mode="normal" in2="effect1_dropShadow_118_5829" result="effect2_dropShadow_118_5829" />
            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_118_5829" result="shape" />
        </filter>
    </defs>
</svg>


@code
{
    public string MaskId { get; set; } = ComponentIdGenerator.Generate();
    public string LinearGradientId { get; set; } = ComponentIdGenerator.Generate();
    public string ClipPathId { get; set; } = ComponentIdGenerator.Generate();
}
