﻿@inherits CustomDOMComponentBase
@using Gizmo.Client.UI.ViewModels

<div class="giz-global-search-result-card @(Result.Type == View.SearchResultTypes.Executables ? "giz-global-search-result-card--app" : "giz-global-search-result-card--product")" @onclick="OnClickHandler">
    <div class="giz-global-search-result-card__image">
        @if (Result.Type == View.SearchResultTypes.Executables)
        {

            <GizImage ImageType="Gizmo.UI.ImageType.Application" ImageId="@Result.ImageId">
                <EmptyResultPlaceholder>
                    <div class="giz-default-image">
                        <img src="_content/Gizmo.Client.UI/img/no-app-image.svg" alt='loading' />
                    </div>
                </EmptyResultPlaceholder>
            </GizImage>

        }
        else
        {

            <GizImage ImageType="Gizmo.UI.ImageType.ProductDefault" ImageId="@Result.ImageId">
                <EmptyResultPlaceholder>
                    <div class="giz-default-image">
                        <img src="_content/Gizmo.Client.UI/img/no-product-image.svg" alt='loading' />
                    </div>
                </EmptyResultPlaceholder>
            </GizImage>

        }
    </div>
    <div class="giz-global-search-result-card__category">
        @if (Result.Type == View.SearchResultTypes.Executables)
        {
            <HeaderGlobalSearchResultCardAppCategory @key="Result" ApplicationCategoryId="@Result.CategoryId" />
        }
        else
        {
            <HeaderGlobalSearchResultCardProductGroup @key="Result" ProductGroupId="@Result.CategoryId" />
        }
    </div>
    <div class="giz-global-search-result-card__title">@Result.Name</div>
    <div class="giz-global-search-result-card__action">
        <Button Variant="ButtonVariants.Outline" @onclick="OnClickActionButtonHandler">@(Result.Type == View.SearchResultTypes.Executables ? @LocalizationService.GetString("GIZ_GEN_LAUNCH") : @LocalizationService.GetString("GIZ_GEN_ADD_TO_CART"))</Button>
    </div>
</div>
