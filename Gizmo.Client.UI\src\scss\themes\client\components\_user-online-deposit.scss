//=============== Spinner ================//

.giz-user-online-deposit {
    color: #FAFAFA;
    width: 45.6rem;
    padding: 2rem;
    background-color: #22272B;
    border-radius: 0 0 0.8rem 0.8rem;
    box-shadow: 0px 32px 64px rgba(0, 0, 0, 0.37), 0px 2px 21px rgba(0, 0, 0, 0.37);

    .giz-button-group {
        margin: 0.8rem 0 2.4rem 0;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(12.8rem, 1fr));
        gap: 0.8rem;
        border: none;

        .giz-button {
            flex: 1;
            border-radius: 0.8rem;
            background-color: #F4F4F4;
            color: #2B2D33;
            //Font ?
            font-weight: 500;
            font-size: 1.6rem;
            line-height: 2.0rem;

            &.selected {
                background-color: #0078D2;
                color: #FFFFFF;
            }
        }
    }

    &__body {
        @include font-m-theme-client($font-weight-bold-theme-client);

        .giz-input-control {
            .giz-input-label {
                @include font-s-theme-client($font-weight-light-theme-client);
            }
        }
    }

    &__summary {
        border-top: 0.1rem solid rgba(246, 251, 253, 0.06);
        border-bottom: 0.1rem solid rgba(246, 251, 253, 0.06);
        padding: 1.2rem 0;
        //margin-top: 2.4rem;
        margin-bottom: 2.9rem;
        @include font-m-theme-client($font-weight-light-theme-client);

        &__amount {
            @include font-m-theme-client($font-weight-bold-theme-client);
        }
    }

    &__submitted {
        &__summary {
            //border-top: 0.1rem solid rgba(246, 251, 253, 0.06);
            border-bottom: 0.1rem solid rgba(246, 251, 253, 0.06);
            padding: 1.2rem 0;
            margin-bottom: 2.4rem;
            @include font-m-theme-client($font-weight-bold-theme-client);

            &__amount {
                @include font-m-theme-client($font-weight-light-theme-client);
            }
        }

        &__qr {
            display: flex;
            justify-content: center;
            position: relative;
            border: 0.1rem solid rgba(246, 251, 253, 0.06);
            border-radius: 0.5rem;
            margin-bottom: 2.4rem;
            @include font-m-theme-client($font-weight-regular-theme-client);

            &__image {
                margin: 2.0rem;

                svg {
                    width: 300px;
                    height: 300px;
                }
            }

            &__label {
                color: rgba(255, 255, 255, 0.6);
                background-color: #22272B;
                padding: 0 1.4rem;
                position: absolute;
                top: 0;
                left: 50%;
                transform: translate(calc(-50%), calc(-50%));
            }
        }

        &__action {
            display: flex;
            justify-content: center;
            position: relative;
            border: 0.1rem solid rgba(246, 251, 253, 0.06);
            border-radius: 0.5rem;
            @include font-m-theme-client($font-weight-light-theme-client);

            & > .giz-button {
                margin: 2.0rem;
            }

            &__label {
                color: rgba(255, 255, 255, 0.6);
                background-color: #22272B;
                padding: 0 1.4rem;
                position: absolute;
                top: 0;
                left: 50%;
                transform: translate(calc(-50%), calc(-50%));
                @include font-m-theme-client($font-weight-light-theme-client);
            }
        }
    }
}

.giz-user-online-deposit-dialog {
    color: #FAFAFA;
    width: 60.0rem;

    .giz-button-group {
        margin: 0.8rem 0 2.4rem 0;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(12.8rem, 1fr));
        gap: 0.8rem;
        border: none;

        .giz-button {
            flex: 1;
            border-radius: 0.8rem;
            background-color: #F4F4F4;
            color: #2B2D33;
            //Font ?
            font-weight: 500;
            font-size: 1.6rem;
            line-height: 2.0rem;

            &.selected {
                background-color: #0078D2;
                color: #FFFFFF;
            }
        }
    }

    .giz-client-dialog__body {
        @include font-m-theme-client($font-weight-bold-theme-client);
    }

    &__summary {
        border-top: 0.1rem solid rgba(246, 251, 253, 0.06);
        border-bottom: 0.1rem solid rgba(246, 251, 253, 0.06);
        padding: 1.2rem 0;
        margin-top: 2.4rem;
        @include font-m-theme-client($font-weight-light-theme-client);
    }

    &__submitted {
        &__summary {
            border-top: 0.1rem solid rgba(246, 251, 253, 0.06);
            border-bottom: 0.1rem solid rgba(246, 251, 253, 0.06);
            padding: 1.2rem 0;
            margin-bottom: 2.4rem;
            @include font-m-theme-client($font-weight-bold-theme-client);
        }

        &__qr {
            display: flex;
            justify-content: center;
            position: relative;
            border: 0.1rem solid rgba(246, 251, 253, 0.06);
            border-radius: 0.5rem;

            &__image {
                margin: 2.0rem;
            }

            &__label {
                color: rgba(255, 255, 255, 0.6);
                background-color: #22272B;
                padding: 0 1.4rem;
                position: absolute;
                top: 0;
                left: 50%;
                transform: translate(calc(-50%), calc(-50%));
            }
        }

        &__action {
            display: flex;
            justify-content: center;
            position: relative;
            border: 0.1rem solid rgba(246, 251, 253, 0.06);
            border-radius: 0.5rem;

            & > .giz-button {
                margin: 2.0rem;
            }

            &__label {
                color: rgba(255, 255, 255, 0.6);
                background-color: #22272B;
                padding: 0 1.4rem;
                position: absolute;
                top: 0;
                left: 50%;
                transform: translate(calc(-50%), calc(-50%));
                @include font-m-theme-client($font-weight-light-theme-client);
            }
        }
    }
}

.giz-user-online-deposit-clear-button {
    padding: 2rem;
}