﻿@namespace Gizmo.Client.UI.Pages
@inherits CustomDOMComponentBase
@layout _Layout_Login
@using System.Threading;
@using Gizmo.UI;
@using Microsoft.AspNetCore.Components.Forms

<LoginCard>
    <CardHeader>
        <div class="giz-login-links">
            <div class="giz-login-new-user">@LocalizationService.GetString("GIZ_LOGIN_ALREADY_A_MEMBER") <a href="@ClientRoutes.LoginRoute">@LocalizationService.GetString("GIZ_REGISTRATION_SIGN_IN")</a></div>
        </div>
        <div class="giz-login-alerts">
            @if (ViewState.HasError)
            {
                <Toast CanClose="true" AlertType="AlertTypes.Danger" Text="@ViewState.ErrorMessage" />
            }
        </div>
    </CardHeader>
    <CardBody>
        <EditForm EditContext="@UserRegistrationBasicFieldsService.EditContext">
            <div class="giz-login-title">@LocalizationService.GetString("GIZ_REGISTRATION_SIGN_UP_TITLE")</div>
            <div class="giz-registration-form-input">
                <TextInput UpdateOnInput="true"
                           Value="@ViewState.Username"
                           ValueChanged="@((string value) => UserRegistrationBasicFieldsService.SetUsername(value))"
                           ValueExpression="@(() => ViewState.Username)"
                           Size="InputSizes.Small"
                           IsFullWidth="true"
                           Label="@LocalizationService.GetString("GIZ_GEN_USERNAME")" />
            </div>
            <div class="giz-registration-form-input giz-form-input-columns">
                <div class="giz-password-tooltip-root">
                    <PasswordInput ShowRevealButton="true"
                                   UpdateOnInput="true"
                                   Value="@ViewState.Password"
                                   ValueChanged="@((string value) => UserRegistrationBasicFieldsService.SetPassword(value))"
                                   ValueExpression="@(() => ViewState.Password)"
                                   Size="InputSizes.Small"
                                   IsFullWidth="true"
                                   Label="@LocalizationService.GetString("GIZ_GEN_PASSWORD")" />
                    <PasswordTooltip Password="@ViewState.Password" />
                </div>
                <PasswordInput UpdateOnInput="true"
                               Value="@ViewState.RepeatPassword"
                               ValueChanged="@((string value) => UserRegistrationBasicFieldsService.SetRepeatPassword(value))"
                               ValueExpression="@(() => ViewState.RepeatPassword)"
                               Size="InputSizes.Small"
                               IsFullWidth="true"
                               Label="@LocalizationService.GetString("GIZ_GEN_CONFIRM_PASSWORD")" />
            </div>
            <div class="giz-registration-form-input giz-form-input-columns">
                @if (UserRegistrationViewState.DefaultUserGroupRequiredInfo.FirstName)
                {
                    <TextInput Value="@ViewState.FirstName"
                               ValueChanged="@((string value) => UserRegistrationBasicFieldsService.SetFirstName(value))"
                               ValueExpression="@(() => ViewState.FirstName)"
                               Size="InputSizes.Small"
                               IsFullWidth="true"
                               Label="@LocalizationService.GetString("GIZ_USER_FIRST_NAME")" />
                }
                @if (UserRegistrationViewState.DefaultUserGroupRequiredInfo.LastName)
                {
                    <TextInput Value="@ViewState.LastName"
                               ValueChanged="@((string value) => UserRegistrationBasicFieldsService.SetLastName(value))"
                               ValueExpression="@(() => ViewState.LastName)"
                               Size="InputSizes.Small"
                               IsFullWidth="true"
                               Label="@LocalizationService.GetString("GIZ_USER_LAST_NAME")" />
                }
            </div>
            <div class="giz-registration-form-input giz-form-input-columns">
                @if (UserRegistrationViewState.DefaultUserGroupRequiredInfo.BirthDate)
                {
                    <MaskedDateInput Value="@ViewState.BirthDate"
                                     ValueChanged="@((DateTime? value) => UserRegistrationBasicFieldsService.SetBirthDate(value))"
                                     ValueExpression="@(() => ViewState.BirthDate)"
                                     Size="InputSizes.Small"
                                     IsFullWidth="true"
                                     Label="@LocalizationService.GetString("GIZ_USER_BIRTH_DATE")" />
                }
                @if (UserRegistrationViewState.DefaultUserGroupRequiredInfo.Sex)
                {
                    <Select Value="@ViewState.Sex"
                            ValueChanged="@((Sex value) => UserRegistrationBasicFieldsService.SetSex(value))"
                            ValueExpression="@(() => ViewState.Sex)"
                            Size="InputSizes.Small"
                            HandleSVGIcon="Icons.Select_Client"
                            IsFullWidth="true"
                            Label="@LocalizationService.GetString("GIZ_USER_GENDER")"
                            OpenDirection="PopupOpenDirections.Cursor"
                            PopupClass="giz-scrollbar--v">
                        <SelectItem Value="@Sex.Unspecified" Text="@LocalizationService.GetString("GIZ_GEN_SEX_UNSPECIFIED")" />
                        <SelectItem Value="@Sex.Male" Text="@LocalizationService.GetString("GIZ_GEN_SEX_MALE")" />
                        <SelectItem Value="@Sex.Female" Text="@LocalizationService.GetString("GIZ_GEN_SEX_FEMALE")" />
                    </Select>
                }
            </div>
            @if (UserRegistrationViewState.DefaultUserGroupRequiredInfo.Email)
            {
                @if (UserRegistrationViewState.ConfirmationMethod != RegistrationVerificationMethod.Email)
                {
                    <div class="giz-form-input">
                        <TextInput UpdateOnInput="true"
                                   Value="@ViewState.Email"
                                   ValueChanged="@((string value) => UserRegistrationBasicFieldsService.SetEmail(value))"
                                   ValueExpression="@(() => ViewState.Email)"
                                   Size="InputSizes.Small"
                                   IsFullWidth="true"
                                   Label="@LocalizationService.GetString("GIZ_GEN_EMAIL_ADDRESS")" />
                    </div>
                }
            }
            <div>
                <Button IsDisabled="@(ViewState.IsValid == false)"
                        Color="ButtonColors.Accent"
                        Size="ButtonSizes.Large"
                        IsFullWidth="true"
                        @onclick="@UserRegistrationBasicFieldsService.SubmitAsync">@LocalizationService.GetString("GIZ_GEN_CONTINUE")</Button>
            </div>
        </EditForm>
    </CardBody>
    <CardFooter>
        <InputLanguageMenu />
    </CardFooter>
</LoginCard>
