﻿@namespace Gizmo.Client.UI
@inherits CustomDOMComponentBase

@if (ViewState.Picture != null)
{
    <Avatar Image="@ViewState.Picture" Variant="AvatarVariants.Circle" Class="giz-user-avatar" />
}
else
{
    <Icon SVGIcon="Icons.AccountCircle_Client" Class="giz-user-avatar" />
    @*<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9.41701 29.083C11.0557 27.9723 12.736 27.1253 14.458 26.542C16.1807 25.9587 18.028 25.667 20 25.667C21.972 25.667 23.826 25.9587 25.562 26.542C27.2987 27.1253 28.9863 27.9723 30.625 29.083C31.7637 27.6943 32.59 26.25 33.104 24.75C33.618 23.25 33.875 21.6667 33.875 20C33.875 16.0833 32.5417 12.7917 29.875 10.125C27.2083 7.45834 23.9167 6.12501 20 6.12501C16.0833 6.12501 12.7917 7.45834 10.125 10.125C7.45834 12.7917 6.12501 16.0833 6.12501 20C6.12501 21.6667 6.38901 23.25 6.91701 24.75C7.44434 26.25 8.27767 27.6943 9.41701 29.083ZM20 21.375C18.3887 21.375 17.0277 20.8193 15.917 19.708C14.8057 18.5973 14.25 17.2363 14.25 15.625C14.25 14.0137 14.8057 12.6527 15.917 11.542C17.0277 10.4307 18.3887 9.87501 20 9.87501C21.6113 9.87501 22.9723 10.4307 24.083 11.542C25.1943 12.6527 25.75 14.0137 25.75 15.625C25.75 17.2637 25.1943 18.6317 24.083 19.729C22.9723 20.8263 21.6113 21.375 20 21.375ZM20 36.667C17.6947 36.667 15.521 36.2293 13.479 35.354C11.4377 34.4793 9.66701 33.285 8.16701 31.771C6.66701 30.257 5.48634 28.493 4.62501 26.479C3.76367 24.465 3.33301 22.3053 3.33301 20C3.33301 17.6947 3.77067 15.528 4.64601 13.5C5.52067 11.472 6.71501 9.70801 8.22901 8.20801C9.74301 6.70801 11.507 5.52067 13.521 4.64601C15.535 3.77067 17.6947 3.33301 20 3.33301C22.3053 3.33301 24.472 3.77067 26.5 4.64601C28.528 5.52067 30.292 6.70801 31.792 8.20801C33.292 9.70801 34.4793 11.472 35.354 13.5C36.2293 15.528 36.667 17.6947 36.667 20C36.667 22.3053 36.2293 24.465 35.354 26.479C34.4793 28.493 33.292 30.257 31.792 31.771C30.292 33.285 28.528 34.4793 26.5 35.354C24.472 36.2293 22.3053 36.667 20 36.667Z" fill="#FAFAFA"/>
    </svg>*@
}