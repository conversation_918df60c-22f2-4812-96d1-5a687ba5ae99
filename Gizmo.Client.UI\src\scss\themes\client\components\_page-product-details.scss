//=============== Page Product Details ================//

.giz-product-details {
    display: grid;
    grid-template-columns: 1fr min-content;
    height: 100%;
    overflow: hidden;
    flex-grow: 1;
    max-width: 2560px;

    &-wrapper {
        display: flex;
        justify-content: center;
        height: 100%;
    }

    &__product {
        display: grid;
        grid-template-rows: min-content min-content min-content min-content;
        padding: 0 1.6rem 1.6rem 1.6rem;
        height: 100%;
        overflow: auto;

        &__bundled-products {
            margin-top: 2.4rem;
            display: flex;
            gap: 1.6rem;
            min-height: 12rem;
            max-width: 100%;
            overflow: hidden;

            .giz-product-time-image {
                gap: 0.8rem;

                .giz-default-image {
                    width: 4rem;
                    height: 4rem;
                }

                &__time {
                    gap: 0.6rem;

                    &__number {
                        //Font
                        font-style: normal;
                        font-weight: 700;
                        font-size: 15px;
                        line-height: 9px;
                    }

                    &__text {
                        //Font ?
                        font-style: normal;
                        font-weight: 500;
                        font-size: 5.48649px;
                        line-height: 9px;
                    }
                }
            }
        }

        &__navigation {
            display: flex;

            & > div {
                display: flex;
                align-items: center;
                margin: 2.8rem 0;
                cursor: pointer;
                color: #FAFAFA;
                @include font-l-theme-client($font-weight-light-theme-client);
            }

            &__icon {
                display: flex;
                align-items: center;
                margin-right: 1.0rem;
            }
        }

        &__info {
            display: grid;
            grid-template-columns: min-content 1fr min-content;
            gap: 4.0rem;
            max-height: 33.3rem;

            &__image {
                width: 29.9rem;
                height: 33.2rem;
                background-color: rgba(0, 0, 0, 0.8);
                border-radius: 1.6rem;
                overflow: hidden;
                /*img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }*/

                .giz-default-image {
                    img {
                        width: unset;
                        height: unset;
                    }
                }
            }

            &__basic {
                display: grid;
                grid-template-rows: min-content min-content min-content 1fr min-content;
                max-width: 70.0rem;
                width: 100%;
                overflow: hidden;

                &__category {
                    margin-bottom: 1.6rem;
                    @include font-xl-theme-client($font-weight-regular-theme-client);
                }

                &__title {
                    max-height: 8.8rem;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    //Font ?
                    font-size: 3.6rem;
                    line-height: 4.4rem;
                    font-weight: 500;
                }

                .giz-time-product-host-groups {
                    gap: 0.8rem;
                    margin-top: 1.6rem;

                    &-tooltip {
                        display: flex;
                        gap: 0.8rem;
                        white-space: nowrap;
                        //max-width: 40rem;
                        //flex-wrap: wrap;
                    }

                    .giz-time-product-host-group {
                        padding: 0.6rem 0.8rem;
                        background-color: rgba(250, 250, 250, 0.16);
                        border-radius: 0.4rem;
                        color: rgba(255, 255, 255, 0.75);
                        //Font
                        font-family: 'Noto Sans';
                        font-style: normal;
                        font-weight: 500;
                        font-size: 13px;
                        line-height: 20px;

                        &--additional { //
                            padding: 0.6rem 0.8rem;
                            background-color: rgba(250, 250, 250, 0.16);
                            border-radius: 0.4rem;
                            color: rgba(255, 255, 255, 0.75);
                            //Font
                            font-family: 'Noto Sans';
                            font-style: normal;
                            font-weight: 500;
                            font-size: 13px;
                            line-height: 20px;
                        }

                        &.active {
                            background-color: rgba(0, 120, 210, 0.32);
                        }
                    }
                }

                &__description {
                    margin: 2.4rem 0;
                    max-height: 100%;
                    overflow: hidden;
                    @include font-l-theme-client($font-weight-light-theme-client);
                }

                &__action {
                    display: flex;

                    &__price {
                        display: flex;
                        align-items: center;
                        gap: 1rem;
                        margin-left: 3.2rem;
                        margin-right: 12.0rem;
                        white-space: nowrap;
                        @include font-h1-theme-client($font-weight-bold-theme-client);

                        svg {
                            width: 3.2rem;
                            height: 3.2rem;
                        }
                    }

                    .giz-button--fill {
                        padding: 1.1rem 2.4rem;
                    }

                    .giz-shop-quantity-picker {
                        width: 16.5rem;
                    }
                }
            }

            &__additional {
                margin-top: 4.4rem;

                &__availability {
                    display: grid;
                    grid-template-columns: auto auto auto;
                    padding: 1.6rem;
                    border-radius: 0.8rem;
                    //background: rgba(250, 250, 250, 0.16);
                    background: rgba(0, 120, 210, 0.32);
                    margin-bottom: 1.6rem;

                    .giz-product-availability {
                        white-space: nowrap;
                        margin-right: 7.4rem;

                        &__title {
                            color: rgba(255, 255, 255, 0.75);
                            @include font-s-theme-client($font-weight-light-theme-client);
                        }

                        &__body {
                            color: #FAFAFA;
                            @include font-m-theme-client($font-weight-regular-theme-client);
                        }
                    }
                }

                &__expirations {
                    &__header {
                        color: rgba(255, 255, 255, 0.75);
                        @include font-s-theme-client($font-weight-regular-theme-client);
                    }

                    &__body {
                        display: flex;
                        gap: 0.8rem;
                        margin-top: 0.8rem;
                        white-space: nowrap;

                        .giz-product-expiration {
                            display: flex;
                            gap: 0.5rem;
                            align-items: center;
                            padding: 0.2rem 0.8rem;
                            background: rgba(255, 199, 0, 0.32);
                            border-radius: 0.4rem;
                            //Font
                            font-family: 'Noto Sans';
                            font-style: normal;
                            font-weight: 500;
                            font-size: 13px;
                            line-height: 20px;

                            .giz-icon {
                                color: #FFC700;
                            }
                        }
                    }
                }
            }
        }

        &__related {
            margin-top: 4.0rem;

            &__header {
                margin-bottom: 2.4rem;
                @include font-h5-theme-client($font-weight-bold-theme-client);
            }

            &__body {
                display: grid;
                grid-template-columns: repeat(6, 1fr);
                grid-auto-rows: minmax(42.0rem, 1fr);
                grid-column-gap: 1.6rem;
                grid-row-gap: 1.6rem;
                justify-content: left;
            }
        }
    }

    &__order {
        height: 100%;
        overflow: hidden;
    }
}

.giz-product-availability-show-more {
    align-self: baseline;
    display: flex;
    align-items: center;
    cursor: pointer;
    @include font-s-theme-client($font-weight-light-theme-client);

    span {
        position: relative;

        &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            border-top: 1px solid #0F9FFF;
        }
    }
}