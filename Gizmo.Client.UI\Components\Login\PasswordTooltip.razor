﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase

<div class="giz-password-tooltip">
    <CircularProgressBar Size="CircularProgressBarSizes.Small" Color="@GetColor()" Value="@(_rulesPassed * 25)" Class="giz-password-tooltip__icon" />
    <div class="giz-password-tooltip__body">
        <div class="giz-password-tooltip__body__title">
            @GetPasswordMessage()
        </div>
        <div class="giz-password-tooltip__body__messages">
            <div class="giz-password-tooltip-message @(_lengthRulePassed ? "passed" : "")">
                @LocalizationService.GetString("GIZ_PASSWORD_RULE_LENGTH")
            </div>
            <div class="giz-password-tooltip-message @(_lowerRulePassed ? "passed" : "")">
                @LocalizationService.GetString("GIZ_PASSWORD_RULE_LOWER_CASE")
            </div>
            <div class="giz-password-tooltip-message @(_upperRulePassed ? "passed" : "")">
                @LocalizationService.GetString("GIZ_PASSWORD_RULE_UPPER_CASE")
            </div>
            <div class="giz-password-tooltip-message @(_numberRulePassed ? "passed" : "")">
                @LocalizationService.GetString("GIZ_PASSWORD_RULE_NUMBER")
            </div>
        </div>
    </div>
</div>