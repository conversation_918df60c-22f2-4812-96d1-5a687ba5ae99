﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase

<HostedDialog>
    <div class="giz-client-dialog giz-ads-carousel-item-dialog">
        <div class="giz-client-dialog__header">
            <div class="giz-client-dialog__header__title">@Title</div>
            <div class="giz-client-dialog__header__subtitle">@MediaUrlType</div>
            <IconButton SVGIcon="Icons.Close" Variant="ButtonVariants.Text" Size="ButtonSizes.Small" Class="close-btn" @onclick="() => DismissCallback.InvokeAsync()" />
        </div>
        <div class="giz-ads-carousel-item-dialog-frame" >
            @switch (MediaUrlType)
            {
                case AdvertisementMediaUrlType.YouTube or AdvertisementMediaUrlType.Vk:
                {
                    <iframe 
                            style="width:100%;height:100%"
                            src="@MediaUrl"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            allowfullscreen>
                    </iframe>
                    break;
                }
                default:
                {
                    <video
                        style="width:100%;height:100%" 
                        src="@MediaUrl" 
                        controls
                        autoplay>
                        Your browser does not support the video tag.
                    </video> 
                    break;
                }
            }
        </div>
    </div>
</HostedDialog>
