﻿@namespace Gizmo.Client.UI.Components

<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg" class="giz-product-icon">
<g filter="url(#@FilterId)"
transform="translate(1.5)">
<path fill-rule="evenodd"
clip-rule="evenodd"
d="M 11.9686,12.8045 5.2292,14.6491 2.28577,3.42854 9.0252,1.58397 c 0.912,-0.24971 1.6743,0.2 1.9183,1.13086 l 2.1326,8.12967 c 0.2451,0.9315 -0.1955,1.7103 -1.1075,1.96 z M 7.53777,12.2103 10.2881,11.4577 10.0669,10.6148 7.31662,11.3674 Z m 1.548,-6.7189 C 8.83377,4.53026 7.86177,3.96054 6.91548,4.2194 5.96977,4.47826 5.40691,5.46797 5.65891,6.42911 c 0.252,0.96172 1.224,1.53086 2.17029,1.272 C 8.77491,7.44226 9.33834,6.45254 9.08577,5.4914 Z M 10.7263,8.62797 5.77491,9.98283 5.99605,10.8263 10.9475,9.4714 Z"
fill="#ffac33" />
</g>
<defs>
<filter id="@FilterId"
x="0.28576699"
y="1.51581"
width="14.8573"
height="17.133301"
filterUnits="userSpaceOnUse"
color-interpolation-filters="sRGB">
<feFlood flood-opacity="0"
result="BackgroundImageFix" />
<feColorMatrix in="SourceAlpha"
type="matrix"
values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
result="hardAlpha" />
<feOffset dy="1" />
<feGaussianBlur stdDeviation="0.5" />
<feColorMatrix type="matrix"
values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0" />
<feBlend mode="normal"
in2="BackgroundImageFix"
result="effect1_dropShadow_2467_70942" />
<feColorMatrix in="SourceAlpha"
type="matrix"
values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
result="hardAlpha" />
<feOffset dy="2" />
<feGaussianBlur stdDeviation="1" />
<feColorMatrix type="matrix"
values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0" />
<feBlend mode="normal"
in2="effect1_dropShadow_2467_70942"
result="effect2_dropShadow_2467_70942" />
<feBlend mode="normal"
in="SourceGraphic"
in2="effect2_dropShadow_2467_70942"
result="shape" />
</filter>
</defs>
</svg>

@code
{
    public string FilterId { get; set; } = ComponentIdGenerator.Generate();
}
