//=============== Layout ================//

.giz-container {
    .giz-app__header {
        background-color: #22272B;
        border-bottom: 0.1rem solid rgba(246, 251, 253, 0.06);
        height: 6.4rem;
    }
}

.giz-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    overflow: hidden;

    &:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(180deg, rgba(12, 15, 17, 0.0001) 0%, rgba(12, 15, 17, 0.0156863) 9.22%, rgba(12, 15, 17, 0.054902) 18.76%, rgba(12, 15, 17, 0.117647) 28.48%, rgba(12, 15, 17, 0.2) 38.19%, rgba(12, 15, 17, 0.290196) 47.75%, rgba(12, 15, 17, 0.392157) 56.99%, rgba(12, 15, 17, 0.501961) 65.75%, rgba(12, 15, 17, 0.607843) 73.87%, rgba(12, 15, 17, 0.709804) 81.18%, rgba(12, 15, 17, 0.8) 87.52%, rgba(12, 15, 17, 0.846259) 90.45%, rgba(12, 15, 17, 0.882353) 92.74%, rgba(12, 15, 17, 0.90408) 92.75%, rgba(12, 15, 17, 0.945098) 96.66%, rgba(12, 15, 17, 0.959815) 99.12%, rgba(12, 15, 17, 0.978709) 99.13%, rgba(12, 15, 17, 0.984314) 99.14%, rgba(12, 15, 17, 0.986777) 99.98%, rgba(12, 15, 17, 0.996851) 99.99%, #0C0F11 100%, #0C0F11 100%, #0C0F11 100%);
        backdrop-filter: blur(0.4rem);
    }

    img {
        width: inherit;
    }
}