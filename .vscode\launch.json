{"version": "0.2.0", "configurations": [{"name": "Launch core", "type": "coreclr", "preLaunchTask": "build", "request": "launch", "program": "dotnet", "args": ["run"], "cwd": "${workspaceFolder}/Gizmo.Client.UI.Host.Web", "env": {"ASPNETCORE_ENVIRONMENT": "Development"}}, {"name": "Launch Edge browser", "type": "msedge", "request": "launch", "timeout": 30000, "url": "https://localhost:5001", "webRoot": "${workspaceFolder}/Gizmo.Client.UI.Host.Web", "inspectUri": "{wsProtocol}://{url.hostname}:{url.port}/_framework/debug/ws-proxy?browser={browserInspectUri}"}]}