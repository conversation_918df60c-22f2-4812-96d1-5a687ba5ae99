//=============== Page App Details ================//

.giz-app-details {
    display: grid;
    grid-template-columns: 1fr min-content;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .giz-client-tab__content {
        gap: 2.4rem;
    }

    &__app {
        justify-self: center;
        display: grid;
        grid-template-rows: min-content 1fr;
        width: 118.6rem;
        height: 100%;
        overflow: hidden;

        &__navigation {
            display: flex;

            & > div {
                display: flex;
                align-items: center;
                margin: 2.8rem 0;
                cursor: pointer;
                color: #FAFAFA;
                @include font-l-theme-client($font-weight-light-theme-client);
            }

            &__icon {
                display: flex;
                align-items: center;
                margin-right: 1.0rem;
            }
        }

        &__info {
            display: grid;
            grid-template-columns: min-content 1fr;
            column-gap: 4.0rem;
            row-gap: 1.7rem;
            width: 100%;
            height: 100%;
            overflow: hidden;

            &__main {
                overflow: auto;
            }

            &__image {
                width: 22.2rem;
                height: 33.3rem;
                background-color: rgba(0, 0, 0, 0.8);
                border-radius: 1.6rem;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                }

                .giz-default-image {
                    img {
                        width: unset;
                        height: unset;
                    }
                }
            }

            &__details {
                color: #FAFAFA;
                display: grid;
                grid-template-rows: min-content min-content 1fr min-content;
                width: 70.0rem;
                overflow: hidden;

                .giz-button {
                    padding: 0.5rem 1.7rem;
                }

                &__category {
                    margin-bottom: 1.6rem;
                    @include font-xl-theme-client($font-weight-regular-theme-client);
                }

                &__title {
                    margin-bottom: 2.4rem;
                    //Font ?
                    font-size: 3.6rem;
                    line-height: 4.4rem;
                    font-weight: 500;
                }

                &__description {
                    @include font-l-theme-client($font-weight-light-theme-client);
                }

                &__executables {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    margin: 3.6rem 0;
                }
            }

            &__additional-content {
                width: 100%;
                overflow: hidden;
                margin-top: 4.2rem;

                &__tags {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 1rem;
                    //margin-bottom: 4.2rem;

                    .giz-app-tag {
                        color: rgba(255, 255, 255, 0.75);
                        background-color: rgba(250, 250, 250, 0.16);
                        border-radius: 0.4rem;
                        padding: 0.2rem 1.2rem;
                        //Font ?
                        font-weight: 400;
                        font-size: 1.6rem;
                        line-height: 3.0rem;
                    }
                }

                &__media {
                    margin-top: 2.4rem;
                    height: 16.4rem;
                    overflow: hidden;
                }
            }
        }
    }

    &__leaderboard {
    }

    &-card-brand-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-auto-rows: min-content;
        row-gap: 2.4rem;
        column-gap: 1.8rem;
        margin-top: 2.4rem;
        white-space: nowrap;
        color: #808191;
        @include font-s-theme-client($font-weight-regular-theme-client);

        .giz-app-details-card-brand-text {
            text-align: right;
            color: #FFFFFF;
        }
    }
}

.giz-app-media-item {
    width: 29.2rem;
    height: 16.4rem;
    overflow: hidden;

    img {
        width: 100%;
        height: 100%;
    }
}

.giz-app-link-item {
    & > a {
        color: #57BCFF;
        @include font-m-theme-client($font-weight-light-theme-client);
    }
}