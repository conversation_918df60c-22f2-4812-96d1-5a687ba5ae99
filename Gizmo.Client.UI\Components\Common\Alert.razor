﻿@namespace Gizmo.Client.UI.Components
@using Gizmo.UI;
@inherits CustomDOMComponentBase

<div class="@Class @ClassName">
    <div class="giz-alert__icon">
        @switch (AlertType)
        {
            case AlertTypes.Success:

                <Icon SVGIcon="Icons.Success_Client" />

                break;

            case AlertTypes.Warning:

                <Icon SVGIcon="Icons.Warning_Client" />

                break;

            default:

                <Icon SVGIcon="Icons.Info_Client" />

                break;
        }
    </div>
    <div class="giz-alert__body">
        <div class="giz-alert__body__title">
            @Title
        </div>
        <div class="giz-alert__body__text">
            @Text
        </div>
    </div>
</div>