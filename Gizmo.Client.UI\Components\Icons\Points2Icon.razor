﻿@namespace Gizmo.Client.UI.Components

<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.05875 13.4237C3.73375 14.545 6.66625 15.2887 10 15.2887C13.3337 15.2887 16.2662 14.545 17.9412 13.4237C18.46 13.9037 18.75 14.4375 18.75 15C18.75 17.07 14.8287 18.75 10 18.75C5.17125 18.75 1.25 17.07 1.25 15C1.25 14.4375 1.54 13.9037 2.05875 13.4237ZM17.9412 8.42375C18.46 8.90375 18.75 9.4375 18.75 10C18.75 12.07 14.8287 13.75 10 13.75C5.17125 13.75 1.25 12.07 1.25 10C1.25 9.4375 1.54 8.90375 2.05875 8.42375C3.73375 9.545 6.66625 10.2887 10 10.2887C13.3337 10.2887 16.2662 9.545 17.9412 8.42375ZM10 1.25C14.8287 1.25 18.75 2.93 18.75 5C18.75 7.07 14.8287 8.75 10 8.75C5.17125 8.75 1.25 7.07 1.25 5C1.25 2.93 5.17125 1.25 10 1.25Z" fill="#F2C94C"/>
</svg>

@code
{
    public string FilterId { get; set; } = ComponentIdGenerator.Generate();
    public string Linear0Id { get; set; } = ComponentIdGenerator.Generate();
    public string Linear1Id { get; set; } = ComponentIdGenerator.Generate();
}
