﻿@namespace Gizmo.Client.UI.Components
@using Gizmo.UI;
@inherits CustomDOMComponentBase

<HostedDialog>
    <div class="giz-client-dialog">
        <div class="giz-client-dialog__header">
            <div class="giz-client-dialog__header__title">@((MarkupString)Title)</div>
            @if (DisplayOptions.Closable)
            {
                <IconButton SVGIcon="Icons.Close" Variant="ButtonVariants.Text" Size="ButtonSizes.Small" Class="close-btn" @onclick="() => CloseDialog(AlertDialogResultButton.None)" />
            }
        </div>
        
        <div class="giz-client-alert-dialog">
            @switch (Icon)
            {
                case AlertTypes.Danger:

                    <div class="giz-client-alert-dialog__icon giz-client-alert-dialog__icon--error">
                        <Icon Size="IconSizes.Large" SVGIcon="Icons.Close_Client" />
                    </div>

                    break;
            }
            <div class="giz-client-alert-dialog__body giz-scrollbar--v">
                <div class="giz-client-alert-message">@((MarkupString)Message)</div>
            </div>
            <div class="giz-client-alert-dialog__footer">
                @switch (Buttons)
                {
                    case AlertDialogButtons.OK:
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.OK))" Text="@LocalizationService.GetString("GIZ_GEN_OK")" />
             
                        break;

                    case AlertDialogButtons.OKCancel:
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.OK))" Text="@LocalizationService.GetString("GIZ_GEN_OK")" />
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.Cancel))" Text="@LocalizationService.GetString("GIZ_GEN_CANCEL")" />
             
                        break;

                    case AlertDialogButtons.AbortRetryIgnore:
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.Abort))" Text="@LocalizationService.GetString("GIZ_GEN_ABORT")" />
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.Retry))" Text="@LocalizationService.GetString("GIZ_GEN_RETRY")" />
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.Ignore))" Text="@LocalizationService.GetString("GIZ_GEN_IGNORE")" />
             
                        break;

                    case AlertDialogButtons.YesNoCancel:
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.Yes))" Text="@LocalizationService.GetString("GIZ_GEN_YES")" />
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.No))" Text="@LocalizationService.GetString("GIZ_GEN_NO")" />
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.Cancel))" Text="@LocalizationService.GetString("GIZ_GEN_CANCEL")" />

                        break;

                    case AlertDialogButtons.YesNo:
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.Yes))" Text="@LocalizationService.GetString("GIZ_GEN_YES")" />
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.No))" Text="@LocalizationService.GetString("GIZ_GEN_NO")" />

                        break;

                    case AlertDialogButtons.RetryCancel:
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.Retry))" Text="@LocalizationService.GetString("GIZ_GEN_RETRY")" />
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.Cancel))" Text="@LocalizationService.GetString("GIZ_GEN_CANCEL")" />
             
                        break;

                    case AlertDialogButtons.CancelTryContinue:
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.Cancel))" Text="@LocalizationService.GetString("GIZ_GEN_CANCEL")" />
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.TryAgain))" Text="@LocalizationService.GetString("GIZ_GEN_RETRY")" />
                           <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog(AlertDialogResultButton.Continue))" Text="@LocalizationService.GetString("GIZ_GEN_CONTINUE")" />
             
                        break;
                }
            </div>
        </div>
    </div>
</HostedDialog>
