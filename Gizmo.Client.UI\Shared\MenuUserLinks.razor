﻿@namespace Gizmo.Client.UI.Shared
@inherits CustomDOMComponentBase
@using Gizmo.Client.UI.ViewModels

<div class="giz-dropdown-menu @(_isOpen ? "open" : "")"
     id="@Id"
     @ref="@Ref">
    <div class="giz-dropdown-menu__content giz-user-links">
        
        <a href="@ClientRoutes.UserProfileRoute" class="giz-user-links-item" @onclick="OnClickLinkHandler">
            <div class="giz-user-links-item__icon">
                <Icon SVGIcon="Icons.User_Client" />
            </div>
            <div class="giz-user-links-item-title">@LocalizationService.GetString("GIZ_USER_MENU_PROFILE")</div>
        </a>
                
        @*<div class="giz-user-links-item">
            <div class="giz-user-links-item__icon">
                <Icon SVGIcon="Icons.Help_Client" />
            </div>
            <div class="giz-user-links-item-title">@LocalizationService.GetString("GIZ_USER_MENU_HELP")</div>
        </div>*@
                
        <div class="giz-user-links-item" @onclick="OnClickUserLockButtonHandler">
            <div class="giz-user-links-item__icon">
                <Icon SVGIcon="Icons.Lock_Client" />
            </div>
            <div class="giz-user-links-item-title">@LocalizationService.GetString("GIZ_USER_MENU_LOCK_PC")</div>
        </div>
                
        <div class="giz-user-links-item" @onclick="OnClickUserLogoutButtonHandler">
            <div class="giz-user-links-item__icon">
                <Icon SVGIcon="Icons.Logout_Client" />
            </div>
            <div class="giz-user-links-item-title">@LocalizationService.GetString("GIZ_USER_MENU_LOGOUT")</div>
        </div>

    </div>
</div>