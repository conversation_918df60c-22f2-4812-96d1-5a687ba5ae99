//=============== Page Apps ================//

.giz-home-apps {
    display: grid;
    grid-template-rows: min-content 1fr;
    height: 100%;
    flex-grow: 1;
    max-width: 2560px;

    &-wrapper {
        display: flex;
        justify-content: center;
        height: 100%;
    }

    &__header {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.6rem;
        padding: 1.6rem;

        &__quick-launch {
            background-color: rgba(255, 255, 255, 0.01);
            box-shadow: inset 0 0 10.1rem rgba(255, 255, 255, 0.06);
            backdrop-filter: blur(6rem);
            border-radius: 1.6rem;
            max-width: 100%;
            z-index: 10;
            //overflow: hidden;
        }

        &__ads {
            background-color: rgba(255, 255, 255, 0.01);
            box-shadow: inset 0 0 10.1rem rgba(255, 255, 255, 0.06);
            backdrop-filter: blur(6rem);
            border-radius: 1.6rem;
            //padding: 1.6rem;
        }
    }

    .giz-ads-carousel-item__content__text {
        max-width: 34.4rem;
    }
}

.giz-apps__body {
    position: relative;
    overflow: hidden;

    .giz-icon-button {
        [#{$client-theme-comp-attr}] & {
            &.giz-input-button-clear {
                width: 2rem;
                height: 2rem;
                min-width: 2rem;
                min-height: 2rem;
                margin-left: 0.4rem;

                .giz-icon {
                    width: 2rem;
                    height: 2rem;
                    min-width: 2rem;
                    min-height: 2rem;
                    background-color: rgba(0, 120, 210, 0.4);
                    border-radius: 50%;
                    display: inline-flex;
                    justify-content: center;

                    svg {
                        color: #0b1a24;
                        width: 1.4rem;
                        height: 1.4rem;
                    }
                }
            }
        }
    }

    &__content {
        margin-top: 1.6rem;
        padding: 0 1.6rem;
        height: 100%;
        overflow: auto;

        &--stuck {
            //clip-path: inset(8.2rem 0 0 0);
            clip-path: polygon(0% 0%, 0 8.2rem, 0 8.2rem, 0 0, 100% 0, 100% 100%, 0 100%, 0 8.2rem, calc(100% - 1rem) 8.2rem, calc(100% - 1rem) 0);
        }

        &__popular {
            margin-top: 1.6rem;
        }
    }

    .giz-section {
        &__header {
            position: sticky;
            top: 0;
            z-index: 10;
            margin-bottom: 0;
            padding-bottom: 1.6rem;
            //background: #0c0f11;

            &.hidden {
                display: none;
            }
        }

        &--stuck {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            padding: 1.6rem 2.4rem 1.6rem 1.6rem;
            display: none;

            &.visible {
                display: block;
            }
        }
    }
}

.giz-select.app-filter--filled {
    .giz-input-root {
        background-color: #0b1a24;
        border-color: #0b1a24;
        color: #0078D2;

        .giz-icon {
            svg {
                color: #3F8CFF;
            }
        }

        input {
            color: #0078D2;
        }
    }
}

.giz-multi-select.app-filter--filled {
    .giz-input-root {
        background-color: #0b1a24;
        border-color: #0b1a24;
        color: #0078D2;

        .giz-input__icon-right {
            svg {
                color: #3F8CFF;
            }
        }
    }
}

.giz-icon-button {
    [#{$client-theme-comp-attr}] & {
        &.giz-input-button-clear {
            background-color: unset;
            width: 2.4rem;
            height: 2.4rem;
            min-width: 2.4rem;
            min-height: 2.4rem;
            margin-left: 0.4rem;

            .giz-icon {
                width: 2.4rem;
                height: 2.4rem;
                min-width: 2.4rem;
                min-height: 2.4rem;
            }
        }
    }
}

.giz-input-root {
    .giz-input__icon-left {
        margin-right: 0.4rem;
    }

    .giz-input__icon-right {
        margin-left: 0.4rem;
    }
}

.giz-apps-filters {
    .giz-input__icon-left {
        width: 1.6rem;
        height: 1.6rem;
        min-width: 1.6rem;
        min-height: 1.6rem;
    }
}