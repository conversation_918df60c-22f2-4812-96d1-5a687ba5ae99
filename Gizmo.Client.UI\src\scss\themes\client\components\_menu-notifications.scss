//=============== Menu Notifications ================//

.giz-menu-notifications {
    display: grid;
    grid-template-rows: min-content 1fr min-content;
    background-color: #22272B;
    width: 36.8rem;
    min-height: 53.3rem;
    border-radius: 0 0 0.8rem 0.8rem;
    box-shadow: 0px 32px 64px rgba(0, 0, 0, 0.37), 0px 2px 21px rgba(0, 0, 0, 0.37);
    max-height: 100%;
    overflow: hidden;

    &__header {
        color: #E4E6EB;
        padding: 0.8rem 1.6rem;
        @include font-h5-theme-client($font-weight-bold-theme-client);

        span {
            color: rgba(255, 255, 255, 0.75);
            @include font-s-theme-client($font-weight-light-theme-client);
        }
    }

    &__body {
        padding: 0.8rem;
        overflow: auto;
    }

    &__footer {
        display: flex;
        justify-content: center;
        color: #0F7CFE;
        padding: 1.1rem;
        @include font-l-theme-client($font-weight-regular-theme-client);
        border-top: 0.1rem solid rgba(255, 255, 255, 0.2);

        &__action {
            cursor: pointer;
        }
    }
}

.giz-menu-notification-item {
    display: grid;
    grid-template-columns: 1fr min-content;
    grid-template-rows: 1fr min-content;
    gap: 0.4rem;
    padding: 0.8rem;
    position: relative;
    border-radius: 0.8rem;
    white-space: normal;
    transition: background-color 0.2s;

    &:hover {
        background-color: #373839;
    }

    &:last-child {
        border-bottom: none;
    }

    &__text {
        @include font-m-theme-client($font-weight-light-theme-client);
    }

    &__icon {
        display: flex;

        svg {
            fill: $primary-color-theme-client;
            width: 1.2rem;
            height: 1.2rem;
        }
    }

    &__details {
        grid-column-start: 1;
        grid-column-end: 3;
        display: flex;
        justify-content: space-between;
        @include font-s-theme-client($font-weight-light-theme-client);

        &__time {
            color: #2E89FF;
        }

        &__action {
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.4rem;
        }
    }
}

.giz-menu-notification-default-item {
    &-wrapper {
        display: flex;
        align-items: center;
        height: 100%;
    }
}
