﻿@inherits ComponentBase;

<Router AppAssembly="@ComponentDiscoveryService.AppAssembly" AdditionalAssemblies="@ComponentDiscoveryService.AdditionalAssemblies">
    <NotFound>
        <h1>Sorry</h1>
        <p>Sorry, there's nothing at this address.</p>
    </NotFound>
    <Found Context="routeData">
        <ErrorBoundary>
            <ChildContent>
                <RouteView RouteData="@routeData" DefaultLayout="@typeof(_Layout)" />
            </ChildContent>
            <ErrorContent>
                @*Place our error template here*@
            </ErrorContent>
        </ErrorBoundary>
    </Found>
</Router>