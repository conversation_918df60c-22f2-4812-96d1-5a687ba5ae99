﻿@inherits ComponentBase

<div style="height: 100%; overflow: auto">

    <div>
        <RadioButton Label="Test1" GroupName="TestRadio" IsChecked="@(Test == "1")" IsCheckedChanged="test1" />
    </div>
    <div>
        <RadioButton Label="Test2" GroupName="TestRadio" IsChecked="@(Test == "2")" IsCheckedChanged="test2" />
    </div>

    <div>
        <TextInput Label="Test - Small" @bind-Value="Test" />
    </div>

    <ComboButton Progress="45"
                 Color="ButtonColors.Danger"
                 Variant="ButtonVariants.Progress"
                 IsFullWidth="true"
                 OpenDirection="PopupOpenDirections.Cursor">
        <ChildContent>
            Test
        </ChildContent>
        <DropDownItems>

        </DropDownItems>
    </ComboButton>

    <ComboButton Progress="45"
                 Color="ButtonColors.Accent"
                 Variant="ButtonVariants.Progress"
                 IsFullWidth="true"
                 OpenDirection="PopupOpenDirections.Cursor">
        <ChildContent>
            Test
        </ChildContent>
        <DropDownItems>

        </DropDownItems>
    </ComboButton>

    <ComboButton Progress="45"
                 Color="ButtonColors.Primary"
                 Variant="ButtonVariants.Progress"
                 IsFullWidth="true"
                 OpenDirection="PopupOpenDirections.Cursor"
                    @bind-IsOpen="_isopen">
        <ChildContent>
            Test
        </ChildContent>
        <DropDownItems>
            <ListItem SVGIcon="Icons.Terminate_Client" OnClick="OnTerminateClick">
                "GIZ_APPS_TERMINATE"
            </ListItem>
            <ListItem SVGIcon="Icons.Terminate_Client" OnClick="OnTerminateClick2">
                "GIZ_APPS_TERMINATE"
            </ListItem>
        </DropDownItems>
    </ComboButton>

    <Button Size="ButtonSizes.Large"
            Variant="ButtonVariants.Fill"
            Color="ButtonColors.Primary"
            LeftSVGIcon="Icons.AccountBalance_Client"
            Text="Test" />

    <div class="compo-test">
        <RadioButton Label="Test - Small" GroupName="g1" IsDisabled="@Disabled" />
        <RadioButton Label="Test - Small" GroupName="g1" IsDisabled="@Disabled" />
    </div>

    <div class="compo-test">
        <TextInput Label="Test - Small" Size="InputSizes.Small" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent" @bind-Value="Deposits" />
    </div>

    <div>
        @*<CheckBox Label="Disabled" @bind-IsChecked="Disabled" />*@

        <CheckBox Label="Outline" @bind-IsChecked="Outline" IsDisabled="@Disabled" />

        @*<CheckBox Label="Shadow" @bind-IsChecked="Shadow" IsDisabled="@Disabled" />

        <CheckBox Label="Transparent" @bind-IsChecked="Transparent" IsDisabled="@Disabled" />

        <CheckBox Label="Full Width" @bind-IsChecked="FullWidth" IsDisabled="@Disabled" />*@
    </div>

    <div class="compo-test">
        <TextInput Label="Test - Small" Size="InputSizes.Small" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent" @bind-Value="Deposits" />
    </div>
    @*<div class="compo-test">
        <TextInput Label="Test - Medium" Size="InputSizes.Medium" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent" @bind-Value="Deposits" />
    </div>
    <div class="compo-test">
        <TextInput Label="Test - Large" Size="InputSizes.Large" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent" @bind-Value="Mask" />
    </div>*@

    @*<div>
        <Alert AlertType="AlertTypes.Danger" Text="Test" />
        <Alert AlertType="AlertTypes.Success" Text="Test" />
        <Alert AlertType="AlertTypes.Warning" Text="Test" />
        <Alert AlertType="AlertTypes.Info" Text="Test" />
        <Alert AlertType="AlertTypes.Accent" Text="Test" />

        <Toast CanClose="true" AlertType="AlertTypes.Danger" Text="Test" />
        <Toast CanClose="true" AlertType="AlertTypes.Success" Text="Test" />
        <Toast CanClose="true" AlertType="AlertTypes.Warning" Text="Test" />
        <Toast CanClose="true" AlertType="AlertTypes.Info" Text="Test" />
        <Toast CanClose="true" AlertType="AlertTypes.Accent" Text="Test" />
    </div>*@

    @*<div class="compo-test">
        <MaskedPhoneInput Label="Test - Large" Size="InputSizes.Large" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent" OpenDirection="PopupOpenDirections.Cursor" Mask="@Mask" @bind-Value="Phone" Countries="@Countries" @bind-SelectedCountry="SelectedCountry" />
    </div>*@

    <div class="compo-test">
        <MaskedTextInput Label="Test - Large" Size="InputSizes.Large" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent" Mask="@Mask" @bind-Value="Phone" />
    </div>
        
    <div class="compo-test">
        <TextInput Label="Test - Small" Size="InputSizes.Small" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent" @bind-Value="Deposits" />
    </div>

    <div class="compo-test">
        <MaskedDateInput Label="Test - Large" Size="InputSizes.Large" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent" @bind-Value="BirthDate" />
    </div>

    <div class="compo-test">
        <TextInput Label="Test - Small" Size="InputSizes.Small" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent" @bind-Value="Deposits" />
    </div>

    <div>
        @BirthDate
    </div>
    
    <div class="compo-test">
        <Select TValue="int" Label="Test - Small" Size="InputSizes.Small" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent">
            <SelectItem Value="0" Text="Name1" />
            <SelectItem Value="1" Text="Name2" />
            <SelectItem Value="2" Text="Name3" />
        </Select>
    </div>
    
    <div class="compo-test">
        <Select @bind-Value="Variant" Label="Test - Medium" Size="InputSizes.Medium" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent">
            <SelectItem Value="ButtonVariants.Fill" Text="Fill" />
            <SelectItem Value="ButtonVariants.Outline" Text="Outline" />
            <SelectItem Value="ButtonVariants.Text" Text="Text" />
            <SelectItem Value="ButtonVariants.Progress" Text="Progress" />
        </Select>
    </div>
    
    <div class="compo-test">
        <Select @bind-Value="Color" Label="Test - Large" Size="InputSizes.Large" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent">
            <SelectItem Value="ButtonColors.Primary" Text="Primary" />
            <SelectItem Value="ButtonColors.Secondary" Text="Secondary" />
            <SelectItem Value="ButtonColors.Warning" Text="Warning" />
            <SelectItem Value="ButtonColors.Danger" Text="Danger" />
            <SelectItem Value="ButtonColors.Success" Text="Success" />
            <SelectItem Value="ButtonColors.Info" Text="Info" />
            <SelectItem Value="ButtonColors.Accent" Text="Accent" />
        </Select>
    </div>
    
    <div class="compo-test">
        <TextInput Label="Test - Small" Size="InputSizes.Small" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent" @bind-Value="Deposits" />
    </div>

    <div class="compo-test">
        <Button Label="Test" Size="ButtonSizes.ExtraSmall" Variant="@Variant" Color="@Color" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasShadow="@Shadow">Test - Small</Button>
    </div>
    @*<div class="compo-test">
        <Button Label="Test" Size="ButtonSizes.Small" Variant="@Variant" Color="@Color" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasShadow="@Shadow">Test - Small</Button>
    </div>
    <div class="compo-test">
        <Button Label="Test" Size="ButtonSizes.Medium" Variant="@Variant" Color="@Color" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasShadow="@Shadow">Test - Medium</Button>
    </div>
    <div class="compo-test">
        <Button Label="Test" Size="ButtonSizes.Large" Variant="@Variant" Color="@Color" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasShadow="@Shadow">Test - Large</Button>
    </div>
    <div class="compo-test">
        <Button Label="Test" Size="ButtonSizes.ExtraLarge" Variant="@Variant" Color="@Color" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasShadow="@Shadow">Test - ExtraLarge</Button>
    </div>*@
    
    <div class="compo-test">
        <TextInput Label="Test - Small" Size="InputSizes.Small" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent" @bind-Value="Deposits" />
    </div>

    <div class="compo-test">
        <IconButton SVGIcon="Icons.Close" Size="ButtonSizes.ExtraSmall" Variant="@Variant" Color="@Color" IsDisabled="@Disabled" HasShadow="@Shadow" />
    </div>    
    @*<div class="compo-test">
        <IconButton SVGIcon="Icons.Close" Size="ButtonSizes.Small" Variant="@Variant" Color="@Color" IsDisabled="@Disabled" HasShadow="@Shadow" />
    </div>
    <div class="compo-test">
        <IconButton SVGIcon="Icons.Close" Size="ButtonSizes.Medium" Variant="@Variant" Color="@Color" IsDisabled="@Disabled" HasShadow="@Shadow" />
    </div>
    <div class="compo-test">
        <IconButton SVGIcon="Icons.Close" Size="ButtonSizes.Large" Variant="@Variant" Color="@Color" IsDisabled="@Disabled" HasShadow="@Shadow" />
    </div>
    <div class="compo-test">
        <IconButton SVGIcon="Icons.Close" Size="ButtonSizes.ExtraLarge" Variant="@Variant" Color="@Color" IsDisabled="@Disabled" HasShadow="@Shadow" />
    </div>*@
    
    @*<div>
        <CircularProgressBar Size="CircularProgressBarSizes.ExtraSmall" Value="@Deposits" Color="blue" />
    </div>
    <div>
        <CircularProgressBar Size="CircularProgressBarSizes.Small" Value="@Deposits" Color="blue" />
    </div>
    <div>
        <CircularProgressBar Size="CircularProgressBarSizes.Medium" Value="@Deposits" Color="blue" />
    </div>
    <div>
        <CircularProgressBar Size="CircularProgressBarSizes.Large" Value="@Deposits" Color="blue" />
    </div>
    <div>
        <CircularProgressBar Size="CircularProgressBarSizes.ExtraLarge" Value="@Deposits" Color="blue" />
    </div>*@
    @*<div>
        <Icon Size="IconSizes.ExtraSmall" SVGIcon="Icons.Close_Client" BackgroundStyle="IconBackgroundStyles.Circle" BackgroundColor="red" />
    </div>
    <div>
        <Icon Size="IconSizes.Small" SVGIcon="Icons.Close_Client" BackgroundStyle="IconBackgroundStyles.Circle" BackgroundColor="red" />
    </div>
    <div>
        <Icon Size="IconSizes.Medium" SVGIcon="Icons.Close_Client" BackgroundStyle="IconBackgroundStyles.Circle" BackgroundColor="red" />
    </div>
    <div>
        <Icon Size="IconSizes.Large" SVGIcon="Icons.Close_Client" BackgroundStyle="IconBackgroundStyles.Circle" BackgroundColor="red" />
    </div>
    <div>
        <Icon Size="IconSizes.ExtraLarge" SVGIcon="Icons.Close_Client" BackgroundStyle="IconBackgroundStyles.Circle" BackgroundColor="red" />
    </div>*@
    
    <div class="compo-test">
        <TextInput Label="Test - Small" Size="InputSizes.Small" IsFullWidth="@FullWidth" IsDisabled="@Disabled" HasOutline="@Outline" HasShadow="@Shadow" IsTransparent="@Transparent" @bind-Value="Deposits" />
    </div>

    <div>
        <ComboButton Color="ButtonColors.Accent" Size="ButtonSizes.Large">
            <ChildContent>
                LAUNCH
            </ChildContent>
            <DropDownItems>
                <ListItem>
                    Autostart
                </ListItem>
                <ListItem SVGIcon="Icons.Repair_Client">
                    Repair
                </ListItem>
                <ListItem SVGIcon="Icons.Folder_Client">
                    <ChildContent>
                        Personal Files
                    </ChildContent>
                    <NestedList>
                        <ListItem>
                            -123
                        </ListItem>
                        <ListItem>
                            456
                        </ListItem>
                        <ListItem>
                            123
                        </ListItem>
                        <ListItem>
                            456
                        </ListItem>
                        <ListItem>
                            123
                        </ListItem>
                        <ListItem>
                            456
                        </ListItem>
                    </NestedList>
                </ListItem>
                <ListItem SVGIcon="Icons.Close_Client">
                    Terminate
                </ListItem>
            </DropDownItems>
        </ComboButton>
    </div>
    
    @*<div>
        <ComboButton IsFullWidth="true" Color="ButtonColors.Accent" Size="ButtonSizes.Large">
            <ChildContent>
                LAUNCH
            </ChildContent>
            <DropDownItems>
                <ListItem>
                    Autostart
                </ListItem>
                <ListItem SVGIcon="Icons.Repair_Client">
                    Repair
                </ListItem>
                <ListItem SVGIcon="Icons.Folder_Client">
                    <ChildContent>
                        Personal Files
                    </ChildContent>
                    <NestedList>
                        <ListItem>
                            -123
                        </ListItem>
                        <ListItem>
                            456
                        </ListItem>
                        <ListItem>
                            123
                        </ListItem>
                        <ListItem>
                            456
                        </ListItem>
                        <ListItem>
                            123
                        </ListItem>
                        <ListItem>
                            456
                        </ListItem>
                    </NestedList>
                </ListItem>
                <ListItem SVGIcon="Icons.Close_Client">
                    Terminate
                </ListItem>
            </DropDownItems>
        </ComboButton>
    </div>*@
</div>
