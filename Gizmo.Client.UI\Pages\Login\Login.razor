﻿@namespace Gizmo.Client.UI.Pages
@inherits CustomDOMComponentBase
@layout _Layout_Login
@using System.Threading
@using Gizmo.UI;
@using Microsoft.AspNetCore.Components.Forms

<LoginCard>
    <CardHeader>
        <div class="giz-login-links">
            @if (UserRegisterConfigurationViewState.IsEnabled)
            {
                <div class="giz-login-new-user">@LocalizationService.GetString("GIZ_LOGIN_NOT_A_MEMBER") <a @onclick="UserLoginService.OpenRegistrationAsync">@LocalizationService.GetString("GIZ_LOGIN_SIGN_UP_NOW")</a></div>
            }
        </div>
        <div class="giz-login-alerts">
            @if (ViewState.HasLoginError)
            {
                <Toast CanClose="true" OnCloseButtonClick="OnCloseButtonClickHandler" AlertType="AlertTypes.Danger" Text="@ViewState.LoginError" />
            }
            else
            {
                @if (HostReservationViewState.IsReserved)
                {
                    @if (HostReservationViewState.IsLoginBlocked)
                    {
                        <Alert AlertType="AlertTypes.Warning"
                               Title="@LocalizationService.GetString("GIZ_LOGIN_RESERVATION_WARNING_TITLE", HostReservationViewState.Time.Value.ToShortTimeString())"
                               Text="@LocalizationService.GetString("GIZ_HOST_RESERVATION_LOGIN_IS_CURRENTLY_BLOCKED")" />
                        
                    }
                    else
                    {
                        <Alert AlertType="AlertTypes.Warning"
                               Title="@LocalizationService.GetString("GIZ_LOGIN_RESERVATION_WARNING_TITLE", HostReservationViewState.Time.Value.ToShortTimeString())"
                               Text="@LocalizationService.GetString("GIZ_HOST_RESERVATION_LOGIN_IS_CURRENTLY_UNBLOCKED")" />
                    }
                }
            }
        </div>
    </CardHeader>

    <CardBody>
        @if (UserLoginOptions.Value.Enabled)
        {
            <div class="giz-login-title">@LocalizationService.GetString("GIZ_LOGIN_SIGN_IN_TITLE")</div>
            <div>
                <ButtonGroup Class="giz-login-method" IsMandatory="true" SelectedItemsChanged="@SelectLoginType" IsDisabled="@ViewState.IsLogginIn">
                    <Button Name="Username" IsSelected="@(ViewState.LoginType == View.UserLoginType.UsernameOrEmail)">@LocalizationService.GetString("GIZ_GEN_USERNAME")</Button>
                    <Button Name="Phone" IsSelected="@(ViewState.LoginType == View.UserLoginType.MobilePhone)">@LocalizationService.GetString("GIZ_GEN_PHONE_NUMBER")</Button>
                </ButtonGroup>
            </div>
            <EditForm EditContext="@UserLoginService.EditContext" @onkeypress="OnKeyPressHandle">
                <div class="giz-form-input">
                    @if (ViewState.LoginType == View.UserLoginType.UsernameOrEmail)
                    {
                        <TextInput UpdateOnInput="true"
                                   Value="@ViewState.LoginName"
                                   ValueChanged="@((string value) => UserLoginService.SetLoginName(value))"
                                   ValueExpression="@(() => ViewState.LoginName)"
                                   ValidateFunction="@UserLoginService.UsernameCharacterIsValid"
                                   IsDisabled="@ViewState.IsLogginIn"
                                   Size="InputSizes.Medium"
                                   IsFullWidth="true"
                                   Label="@LocalizationService.GetString("GIZ_GEN_USERNAME")" />
                    }
                    else
                    {
                        <MaskedPhoneInput Mask="###-###-####"
                                          AllowMoreDigits="true"
                                          Value="@ViewState.LoginName"
                                          ValueChanged="@((string value) => UserLoginService.SetLoginName(value))"
                                          ValueExpression="@(() => ViewState.LoginName)"
                                          IsDisabled="@ViewState.IsLogginIn"
                                          Size="InputSizes.Medium"
                                          IsFullWidth="true"
                                          Label="@LocalizationService.GetString("GIZ_GEN_PHONE_NUMBER")" />
                    }
                </div>
                <div class="giz-form-input">
                    <PasswordInput ShowRevealButton="true"
                                   UpdateOnInput="true" Value="@ViewState.Password"
                                   ValueChanged="@((string value) => UserLoginService.SetPassword(value))"
                                   ValueExpression="@(() => ViewState.Password)"
                                   IsPasswordVisible="@ViewState.IsPasswordVisible"
                                   IsPasswordVisibleChanged="@((bool value) => UserLoginService.SetPasswordVisible(value))"
                                   IsDisabled="@ViewState.IsLogginIn"
                                   Size="InputSizes.Medium"
                                   IsFullWidth="true"
                                   Label="@LocalizationService.GetString("GIZ_GEN_PASSWORD")" />
                    @if (UserPasswordRecoveryMethodServiceViewState.AvailabledRecoveryMethod != UserRecoveryMethod.None)
                    {
                        <div class="giz-login-forgot-password">
                            <a tabindex="-1" href="@ClientRoutes.PasswordRecoveryRoute">@LocalizationService.GetString("GIZ_LOGIN_FORGOT_YOUR_PASSWORD")</a>
                        </div>
                    }
                </div>
                <div>
                    <Button Color="ButtonColors.Accent" LeftSVGIcon="@(!ViewState.IsLogginIn ? Icons.Login_Client : null)" Size="ButtonSizes.ExtraLarge" IsFullWidth="true" @onclick="@UserLoginService.LoginAsync">
                        @if (ViewState.IsLogginIn)
                        {
                            <Spinner />
                        }
                        else
                        {
                            <div>@LocalizationService.GetString("GIZ_GEN_CONTINUE")</div>
                        }
                    </Button>
                </div>
            </EditForm>
        }
    </CardBody>
    <CardFooter>
        @if (HostQRCodeViewState.IsEnabled)
        {
            <div class="giz-alternative-login">
                @if (UserLoginOptions.Value.Enabled)
                {
                    <div class="giz-alternative-login__separator"><span>@LocalizationService.GetString("GIZ_LOGIN_OR")</span></div>
                }
                <div class="giz-alternative-login__qr">
                    <div class="giz-alternative-login__qr-description">
                        <div class="giz-alternative-login__qr-description__title">@LocalizationService.GetString("GIZ_LOGIN_QR_TITLE")</div>
                        <div class="giz-alternative-login__qr-description__subtitle">@LocalizationService.GetString("GIZ_LOGIN_QR_MESSAGE")</div>
                    </div>
                    @((MarkupString)HostQRCodeViewState.HostQRCode)
                </div>
            </div>
        }
        <InputLanguageMenu />
    </CardFooter>
</LoginCard>
