﻿@namespace Gizmo.Client.UI.Shared
@inherits CustomDOMComponentBase

@if (ViewState.IsLocked)
{
    <div class="giz-login-overlay">
        <div class="giz-host-locked">
            <div class="giz-host-locked__message">
                <Icon Size="IconSizes.Large" SVGIcon="Icons.Lock_Client" />
                <div>@LocalizationService.GetString("GIZ_SYSTEM_LOCKED")</div>
            </div>
        </div>
    </div>
}