﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase
@using System.Globalization

<div class="giz-profile-header__info__stats">
    <div class="giz-profile-header__info__stats-item">

        <Icon SVGIcon="Icons.AccountBalance_Client" />

        <div class="giz-profile-header__info__stats-text">
            <div class="giz-title">@LocalizationService.GetString("GIZ_GEN_BALANCE")</div>
            <div class="giz-numbers">@ViewState.Balance.ToString("C", CultureInfo.CurrentCulture)
            </div>
        </div>
    </div>
    <div class="giz-profile-header__info__stats-item">

        <Points2Icon />

        <div class="giz-profile-header__info__stats-text">
            <div class="giz-title">@LocalizationService.GetString("GIZ_GEN_POINTS")</div>
            <div class="giz-numbers">@ViewState.PointsBalance</div>
        </div>
    </div>
    <div class="giz-profile-header__info__stats-item">

        <Icon SVGIcon="Icons.Schedule_Client" />

        <div class="giz-profile-header__info__stats-text">
            <div class="giz-title">@LocalizationService.GetString("GIZ_GEN_TIME")</div>
            <div class="giz-numbers">@ViewState.Time</div>
        </div>
    </div>
</div>
