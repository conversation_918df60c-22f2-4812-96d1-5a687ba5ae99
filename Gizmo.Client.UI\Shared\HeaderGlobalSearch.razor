﻿@namespace Gizmo.Client.UI.Shared
@inherits CustomDOMComponentBase
@using Gizmo.Client.UI.ViewModels

<div class="giz-header__global-search">
    <div class="giz-global-search @(ViewState.OpenDropDown ? "open" : "") @(!string.IsNullOrEmpty(ViewState.SearchPattern) ? "nonemtpy" : "")" tabindex="0" @onfocusin="OnFocusHandler">

        <Icon SVGIcon="Icons.Search_Client" />

        <input type="text"
               value="@ViewState.SearchPattern"
               placeholder="@LocalizationService.GetString("GIZ_GLOBAL_SEARCH_PLACEHOLDER")"
               @ref="_inputElement"
               @onfocusin="OnFocusInHandler"
               @onfocusout="OnFocusOutHandler"
               @oninput="OnInputHandler"
               @onkeydown="OnInputKeyDownHandler">

        <IconButton SVGIcon="Icons.Close" Size="ButtonSizes.Small" Variant="ButtonVariants.Text" @onclick="@(() => Clear())"
                    Style="@CloseButtonStyleValue" Class="giz-close-button" />

        <div class="giz-global-search-dropdown @(ViewState.OpenDropDown ? "open" : "")"
             id="@Id"
             @ref="@Ref">
            <div class="giz-dropdown-menu__content giz-global-search-dropdown__body">

                @if ((ViewState.ExecutableResults != null && ViewState.ExecutableResults.Count() > 0) || (ViewState.ProductResults != null && ViewState.ProductResults.Count() > 0))
                {
                    @if (ViewState.ExecutableResults != null && ViewState.ExecutableResults.Count() > 0)
                    {
                        <div class="giz-global-search-section">
                            <div class="giz-global-search-section__header">

                                <div>@LocalizationService.GetString("GIZ_GLOBAL_SEARCH_GAMES_AND_APPS")</div>

                                @if (ViewState.ExecutableResults.Count() > 10)
                                {
                                    <div class="giz-global-search-section__header__more" @onclick="@(() => GlobalSearchService.ViewAllResultsAsync(Gizmo.Client.UI.View.SearchResultTypes.Executables))">@LocalizationService.GetString("GIZ_GLOBAL_SEARCH_SHOW_ALL")</div>
                                }
                            </div>
                            <div class="giz-global-search-section__body">

                                @foreach (var result in ViewState.ExecutableResults.Take(10))
                                {
                                    <UniversalExecutable @key="result.Id" ExecutableId="@result.Id" ShowProgressBar="true" ShowExeName="true" ShowAppName="true" />
                                    @*<HeaderGlobalSearchResultCard Result="@result" />*@
                                }

                            </div>
                        </div>
                    }
                    @if (ViewState.ProductResults != null && ViewState.ProductResults.Count() > 0)
                    {
                        <div class="giz-global-search-section">
                            <div class="giz-global-search-section__header">

                                <div>@LocalizationService.GetString("GIZ_GLOBAL_SEARCH_SHOP")</div>

                                @if (ViewState.ProductResults.Count() > 10)
                                {
                                    <div class="giz-global-search-section__header__more" @onclick="@(() => GlobalSearchService.ViewAllResultsAsync(Gizmo.Client.UI.View.SearchResultTypes.Products))">@LocalizationService.GetString("GIZ_GLOBAL_SEARCH_SHOW_ALL")</div>
                                }
                            </div>
                            <div class="giz-global-search-section__body">

                                @foreach (var result in ViewState.ProductResults.Take(10))
                                {
                                    <HeaderGlobalSearchResultCard Result="@result" />
                                }

                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="giz-global-search-empty">
                        @if (ViewState.IsLoading)
                        {
                            <div class="giz-loader">
                                <Spinner />
                            </div>
                        }
                        else
                        {
                            <div class="giz-empty-state">
                                <div class="giz-empty-state__title">@ViewState.EmptyResultTitle</div>
                                <div class="giz-empty-state__text">@ViewState.EmptyResultMessage</div>
                            </div>
                        }
                    </div>
                }

            </div>
        </div>

    </div>
</div>