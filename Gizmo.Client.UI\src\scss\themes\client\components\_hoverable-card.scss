//=============== Hoverable Card ================//

.giz-hoverable-card {
    position: relative;
    height: 100%;

    &:hover {
        .hovered-content-wrapper {
            display: block;
            background: linear-gradient(180deg, rgba(4, 5, 6, 0.45) 0%, rgba(4, 5, 6, 0.9) 91.51%);
        }
    }

    .hoverable-content {
        height: 100%;
    }

    .hovered-content-wrapper {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;

        .hovered-content {
            height: 100%;
        }
    }
}
