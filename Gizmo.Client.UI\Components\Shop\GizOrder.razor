﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase
@using System.Globalization

<div class="giz-order">
    <div class="giz-order__items">
        <div class="giz-order__items__header">@LocalizationService.GetString("GIZ_SHOP_ORDER_TITLE")</div>
        <div class="giz-order__items__body giz-scrollbar--v">
            @if (ViewState.Products.Count() > 0)
            {
                <div class="giz-order__items-wrapper">
                    @foreach (var orderLine in ViewState.Products)
                    {
                        <GizOrderItem @key="orderLine" ProductItemViewState="@orderLine" />
                    }
                </div>
            }
            else
            {
                <div class="giz-no-order-wrapper">
                    <div class="giz-empty-state">
                        <div class="giz-empty-state__title">@LocalizationService.GetString("GIZ_SHOP_NO_PRODUCTS_MESSAGE")</div>
                        <div class="giz-empty-state__text">@LocalizationService.GetString("GIZ_SHOP_NO_PRODUCTS_RETRY_MESSAGE")</div>
                    </div>
                </div>
            }
        </div>
        <div class="giz-order__items__footer">
            <Button IsDisabled="@(ViewState.Products.Count() == 0)" Variant="ButtonVariants.Text" Size="ButtonSizes.Small" LeftSVGIcon="Icons.Trash3_Client" Text="@LocalizationService.GetString("GIZ_GEN_CLEAR")" @onclick="@(() => UserCartService.ClearUserCartProductsAsync())" />
        </div>
    </div>
        
    <div class="giz-order__notes">
        <div class="giz-order__notes__header">@LocalizationService.GetString("GIZ_SHOP_ORDER_NOTE")</div>
        <div class="giz-order__notes__body">
            <TextInput IsTransparent="true"
                       ValidationErrorStyle="ValidationErrorStyles.BorderOnly"
                       Value="@ViewState.Notes"
                       ValueChanged="@((string value) => UserCartService.SetNotes(value))"
                       ValueExpression="@(() => ViewState.Notes)"
                       IsMultiLine="true"
                       IsFullWidth="true"
                       Placeholder="@LocalizationService.GetString("GIZ_SHOP_ORDER_NOTE_PLACEHOLDER")"
                       Class="giz-order-notes" />
        </div>
    </div>

    <div class="giz-order__totals">
        <div class="giz-order-summary">
            <div class="giz-order-summary-text-bold">@LocalizationService.GetString("GIZ_SHOP_SUBTOTAL")</div>

            <div class="giz-order-summary-total">
                <span>@ViewState.Total.ToString("C", CultureInfo.CurrentCulture)</span>
                @if (ViewState.PointsTotal > 0)
                {
                    <span class="giz-order-summary-number-separator">&amp;</span>
                    <span>@(ViewState.PointsTotal)</span>
                    <Points2Icon />
                }
            </div>

            <div class="giz-order-summary-text">@LocalizationService.GetString("GIZ_SHOP_EARNED_POINTS")</div>
            <div class="giz-order-summary-points-award">
                <span>@ViewState.PointsAward</span>
                <PointsAward2Icon />
            </div>
        </div>
        <div>
            <Button Color="ButtonColors.Accent" Size="ButtonSizes.Large" IsFullWidth="true" IsDisabled="@(ViewState.Products.Count() == 0)" @onclick="@(() => PlaceOrder())" Text="@LocalizationService.GetString("GIZ_SHOP_PLACE_ORDER")" />
        </div>
    </div>
</div>
