﻿@namespace Gizmo.Client.UI.Components

<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="giz-points-award-icon">
<mask id="@MaskId" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
<rect width="20" height="20" transform="matrix(1 0 0 -1 0 20)" fill="#D9D9D9"/>
</mask>
<g mask="url(#@MaskId)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.86501 1.84432C7.14967 1.18432 8.52801 0.854322 10 0.854322C11.8333 0.854322 13.4583 1.30932 14.875 2.21932C16.2917 3.12866 17.389 4.34732 18.167 5.87532C18.2917 6.11132 18.3297 6.35432 18.281 6.60432C18.2323 6.85432 18.09 7.03499 17.854 7.14632C17.618 7.25699 17.382 7.25332 17.146 7.13532C16.91 7.01732 16.7293 6.84032 16.604 6.60432C15.9653 5.38232 15.0627 4.41032 13.896 3.68832C12.7293 2.96565 11.4307 2.60432 10 2.60432C8.63867 2.60432 7.38167 2.95165 6.22901 3.64632C5.07634 4.34099 4.16667 5.28532 3.50001 6.47932H5.41701C5.65301 6.47932 5.85767 6.56632 6.03101 6.74032C6.20501 6.91365 6.29201 7.11832 6.29201 7.35432C6.29201 7.59032 6.20501 7.79532 6.03101 7.96932C5.85767 8.14265 5.65301 8.22932 5.41701 8.22932H1.70801C1.47201 8.22932 1.26734 8.14265 1.09401 7.96932C0.920008 7.79532 0.833008 7.59032 0.833008 7.35432V3.64632C0.833008 3.41032 0.920008 3.20532 1.09401 3.03132C1.26734 2.85799 1.47201 2.77132 1.70801 2.77132C1.94401 2.77132 2.14901 2.85799 2.32301 3.03132C2.49634 3.20532 2.58301 3.41032 2.58301 3.64632V4.64632C3.48634 3.43765 4.58034 2.50366 5.86501 1.84432ZM1.17701 11.4063C1.32301 11.191 1.52101 11.0627 1.77101 11.0213C2.00701 10.9793 2.22567 11.0313 2.42701 11.1773C2.62834 11.3233 2.76367 11.5213 2.83301 11.7713C2.93034 12.16 3.05201 12.528 3.19801 12.8753C3.34401 13.2227 3.51401 13.556 3.70801 13.8753C3.83334 14.084 3.88567 14.3097 3.86501 14.5523C3.84367 14.7957 3.73601 14.9937 3.54201 15.1463C3.34734 15.299 3.12834 15.351 2.88501 15.3023C2.64234 15.2537 2.45134 15.1253 2.31201 14.9173C2.00667 14.4587 1.75001 14.0003 1.54201 13.5423C1.33334 13.0837 1.17334 12.6043 1.06201 12.1043C0.992675 11.8543 1.03101 11.6217 1.17701 11.4063ZM4.75001 17.2193C4.69467 16.976 4.74334 16.7503 4.89601 16.5423C5.03467 16.3477 5.22201 16.2297 5.45801 16.1883C5.69401 16.1463 5.91634 16.1877 6.12501 16.3123C6.47234 16.521 6.82301 16.6983 7.17701 16.8443C7.53101 16.9897 7.88867 17.111 8.25001 17.2083C8.48601 17.2777 8.68401 17.4063 8.84401 17.5943C9.00334 17.7817 9.06234 17.9933 9.02101 18.2293C8.97901 18.4793 8.85734 18.6737 8.65601 18.8123C8.45467 18.9517 8.22901 18.9937 7.97901 18.9383C7.45167 18.827 6.95867 18.6777 6.50001 18.4903C6.04134 18.3023 5.59701 18.0697 5.16701 17.7923C4.94434 17.653 4.80534 17.462 4.75001 17.2193ZM14.552 16.1873C14.7953 16.2153 14.9933 16.3267 15.146 16.5213C15.2987 16.7153 15.3507 16.9377 15.302 17.1883C15.2533 17.4377 15.125 17.632 14.917 17.7713C14.4723 18.0493 14.0173 18.2853 13.552 18.4793C13.0867 18.674 12.5833 18.827 12.042 18.9383C11.792 18.9937 11.5627 18.9517 11.354 18.8123C11.146 18.6737 11.021 18.4793 10.979 18.2293C10.9377 17.9933 10.9967 17.7817 11.156 17.5943C11.316 17.4063 11.514 17.2777 11.75 17.2083C12.1247 17.111 12.4893 16.993 12.844 16.8543C13.198 16.7157 13.5347 16.549 13.854 16.3543C14.0767 16.2157 14.3093 16.16 14.552 16.1873ZM17.604 11.1673C17.7987 11.0147 18.014 10.959 18.25 11.0003C18.5 11.0417 18.6943 11.1633 18.833 11.3653C18.9723 11.5667 19.014 11.7923 18.958 12.0423C18.8473 12.5697 18.6913 13.0627 18.49 13.5213C18.2887 13.9793 18.042 14.4307 17.75 14.8753C17.6113 15.084 17.4203 15.216 17.177 15.2713C16.9337 15.3267 16.715 15.278 16.521 15.1253C16.3263 14.9867 16.2153 14.7957 16.188 14.5523C16.16 14.3097 16.2083 14.084 16.333 13.8753C16.5277 13.542 16.698 13.1983 16.844 12.8443C16.9893 12.4897 17.1107 12.125 17.208 11.7503C17.2773 11.5143 17.4093 11.32 17.604 11.1673Z" fill="#1C1B1F"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.86501 1.84432C7.14967 1.18432 8.52801 0.854322 10 0.854322C11.8333 0.854322 13.4583 1.30932 14.875 2.21932C16.2917 3.12866 17.389 4.34732 18.167 5.87532C18.2917 6.11132 18.3297 6.35432 18.281 6.60432C18.2323 6.85432 18.09 7.03499 17.854 7.14632C17.618 7.25699 17.382 7.25332 17.146 7.13532C16.91 7.01732 16.7293 6.84032 16.604 6.60432C15.9653 5.38232 15.0627 4.41032 13.896 3.68832C12.7293 2.96565 11.4307 2.60432 10 2.60432C8.63867 2.60432 7.38167 2.95165 6.22901 3.64632C5.07634 4.34099 4.16667 5.28532 3.50001 6.47932H5.41701C5.65301 6.47932 5.85767 6.56632 6.03101 6.74032C6.20501 6.91365 6.29201 7.11832 6.29201 7.35432C6.29201 7.59032 6.20501 7.79532 6.03101 7.96932C5.85767 8.14265 5.65301 8.22932 5.41701 8.22932H1.70801C1.47201 8.22932 1.26734 8.14265 1.09401 7.96932C0.920008 7.79532 0.833008 7.59032 0.833008 7.35432V3.64632C0.833008 3.41032 0.920008 3.20532 1.09401 3.03132C1.26734 2.85799 1.47201 2.77132 1.70801 2.77132C1.94401 2.77132 2.14901 2.85799 2.32301 3.03132C2.49634 3.20532 2.58301 3.41032 2.58301 3.64632V4.64632C3.48634 3.43765 4.58034 2.50366 5.86501 1.84432ZM1.17701 11.4063C1.32301 11.191 1.52101 11.0627 1.77101 11.0213C2.00701 10.9793 2.22567 11.0313 2.42701 11.1773C2.62834 11.3233 2.76367 11.5213 2.83301 11.7713C2.93034 12.16 3.05201 12.528 3.19801 12.8753C3.34401 13.2227 3.51401 13.556 3.70801 13.8753C3.83334 14.084 3.88567 14.3097 3.86501 14.5523C3.84367 14.7957 3.73601 14.9937 3.54201 15.1463C3.34734 15.299 3.12834 15.351 2.88501 15.3023C2.64234 15.2537 2.45134 15.1253 2.31201 14.9173C2.00667 14.4587 1.75001 14.0003 1.54201 13.5423C1.33334 13.0837 1.17334 12.6043 1.06201 12.1043C0.992675 11.8543 1.03101 11.6217 1.17701 11.4063ZM4.75001 17.2193C4.69467 16.976 4.74334 16.7503 4.89601 16.5423C5.03467 16.3477 5.22201 16.2297 5.45801 16.1883C5.69401 16.1463 5.91634 16.1877 6.12501 16.3123C6.47234 16.521 6.82301 16.6983 7.17701 16.8443C7.53101 16.9897 7.88867 17.111 8.25001 17.2083C8.48601 17.2777 8.68401 17.4063 8.84401 17.5943C9.00334 17.7817 9.06234 17.9933 9.02101 18.2293C8.97901 18.4793 8.85734 18.6737 8.65601 18.8123C8.45467 18.9517 8.22901 18.9937 7.97901 18.9383C7.45167 18.827 6.95867 18.6777 6.50001 18.4903C6.04134 18.3023 5.59701 18.0697 5.16701 17.7923C4.94434 17.653 4.80534 17.462 4.75001 17.2193ZM14.552 16.1873C14.7953 16.2153 14.9933 16.3267 15.146 16.5213C15.2987 16.7153 15.3507 16.9377 15.302 17.1883C15.2533 17.4377 15.125 17.632 14.917 17.7713C14.4723 18.0493 14.0173 18.2853 13.552 18.4793C13.0867 18.674 12.5833 18.827 12.042 18.9383C11.792 18.9937 11.5627 18.9517 11.354 18.8123C11.146 18.6737 11.021 18.4793 10.979 18.2293C10.9377 17.9933 10.9967 17.7817 11.156 17.5943C11.316 17.4063 11.514 17.2777 11.75 17.2083C12.1247 17.111 12.4893 16.993 12.844 16.8543C13.198 16.7157 13.5347 16.549 13.854 16.3543C14.0767 16.2157 14.3093 16.16 14.552 16.1873ZM17.604 11.1673C17.7987 11.0147 18.014 10.959 18.25 11.0003C18.5 11.0417 18.6943 11.1633 18.833 11.3653C18.9723 11.5667 19.014 11.7923 18.958 12.0423C18.8473 12.5697 18.6913 13.0627 18.49 13.5213C18.2887 13.9793 18.042 14.4307 17.75 14.8753C17.6113 15.084 17.4203 15.216 17.177 15.2713C16.9337 15.3267 16.715 15.278 16.521 15.1253C16.3263 14.9867 16.2153 14.7957 16.188 14.5523C16.16 14.3097 16.2083 14.084 16.333 13.8753C16.5277 13.542 16.698 13.1983 16.844 12.8443C16.9893 12.4897 17.1107 12.125 17.208 11.7503C17.2773 11.5143 17.4093 11.32 17.604 11.1673Z" fill="url(#@LinearGradientId)" />
</g>
<g clip-path="url(#@ClipPathId)">
<path d="M9.9997 13.9999C12.0861 13.9999 13.7775 12.3085 13.7775 10.2221C13.7775 8.13569 12.0861 6.44432 9.9997 6.44432C7.91329 6.44432 6.22192 8.13569 6.22192 10.2221C6.22192 12.3085 7.91329 13.9999 9.9997 13.9999Z" fill="#F4900C"/>
<path d="M9.9997 13.5557C12.0861 13.5557 13.7775 11.8643 13.7775 9.7779C13.7775 7.69149 12.0861 6.00012 9.9997 6.00012C7.91329 6.00012 6.22192 7.69149 6.22192 9.7779C6.22192 11.8643 7.91329 13.5557 9.9997 13.5557Z" fill="#FFCC4D"/>
<path d="M9.99978 13.1112C11.718 13.1112 13.1109 11.7183 13.1109 10.0001C13.1109 8.2819 11.718 6.88901 9.99978 6.88901C8.28156 6.88901 6.88867 8.2819 6.88867 10.0001C6.88867 11.7183 8.28156 13.1112 9.99978 13.1112Z" fill="#FFE8B6"/>
<path d="M9.99978 12.8891C11.718 12.8891 13.1109 11.4962 13.1109 9.778C13.1109 8.05978 11.718 6.66689 9.99978 6.66689C8.28156 6.66689 6.88867 8.05978 6.88867 9.778C6.88867 11.4962 8.28156 12.8891 9.99978 12.8891Z" fill="#FFAC33"/>
</g>
<defs>
<linearGradient id="@LinearGradientId" x1="0.833008" y1="9.90812" x2="18.9821" y2="9.90812" gradientUnits="userSpaceOnUse">
<stop stop-color="#F09819"/>
<stop offset="1" stop-color="#EDDE5D"/>
</linearGradient>
<clipPath id="@ClipPathId">
<rect width="8" height="8" fill="white" transform="translate(5.99976 6.00012)"/>
</clipPath>
</defs>
</svg>

@code
{
    public string MaskId { get; set; } = ComponentIdGenerator.Generate();
    public string LinearGradientId { get; set; } = ComponentIdGenerator.Generate();
    public string ClipPathId { get; set; } = ComponentIdGenerator.Generate();
}
