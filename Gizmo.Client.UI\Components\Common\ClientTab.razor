﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase

<div class="@Class @ClassName"
     id="@Id"
     @ref="@Ref">
    <Button Class="giz-client-tab__previous" LeftSVGIcon="Icons.ArrowLeft_Client" Size="ButtonSizes.ExtraSmall" @onclick="OnClickPreviousButton" />
    <div class="giz-client-tab__wrapper">
        <div class="giz-client-tab__content">
            @ChildContent
        </div>
    </div>
    <Button Class="giz-client-tab__next" LeftSVGIcon="Icons.ArrowRight_Client" Size="ButtonSizes.ExtraSmall" @onclick="OnClickNextButton" />
</div>
