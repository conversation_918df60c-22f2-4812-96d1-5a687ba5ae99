﻿{
  "Interface": {
    "Background": "bg-img-2.png",
    "Skin": "override_skin.css",
    "DisableAppDetails": false,
    "DisableProductDetails": false
  },
  //https://learn.microsoft.com/en-us/dotnet/api/system.globalization.numberformatinfo?view=net-6.0
  "CurrencyOptions": {
    "CurrencySymbol": null,
    "CurrencyDecimalDigits": null,
    "CurrencyDecimalSeparator": null,
    "CurrencyGroupSeparator": null,
    "CurrencyGroupSizes": null,
    "CurrencyNegativePattern": null,
    "CurrencyPositivePattern": null
  },
  "UserOnlineDepositOptions": {
    "ShowUserOnlineDeposit": true,
    "MaximumAmount": 100
  },
  "PopularItemsOptions": {
    "MaxPopularProducts": 16,
    "MaxPopularApplications": 16,
    "MaxQuickLaunchExecutables": 10,
    "HomePageMaxItemsPerRow": 8,
    "AppsPageMaxItemsPerRow": 8,
    "ProductsPageMaxItemsPerRow": 6
  },
  "HostQRCodeOptions": {
    "Enabled": true
  },
  "UserLoginOptions": {
    "Enabled": true
  },
  "NotificationsOptions": {
    "DefaultTimeout": null
  },
  "LoginRotatorOptions": {
    "IsEnabled": true,
    "Path": "D:\\Files\\Gizmo\\Gizmo\\Gizmo.Client.UI\\Gizmo.Client.UI\\wwwroot\\img\\rotator\\",
    "RotateEvery": 6
  }
}
