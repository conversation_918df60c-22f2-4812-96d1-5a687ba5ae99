﻿@namespace Gizmo.Client.UI.Components

<svg width="19" height="22" viewBox="0 0 19 22" fill="currentColor" xmlns="http://www.w3.org/2000/svg" class="giz-list-icon">
<g filter="url(#@FilterId)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.9605 15.0057L6.53622 17.3114L2.85693 3.28569L11.2812 0.979979C12.4212 0.667836 13.3741 1.22998 13.6791 2.39355L16.3448 12.5557C16.6512 13.72 16.1005 14.6936 14.9605 15.0057ZM9.42193 14.2628L12.8598 13.3221L12.5834 12.2685L9.1455 13.2093L9.42193 14.2628ZM11.3569 5.86426C11.0419 4.66284 9.82693 3.95069 8.64408 4.27426C7.46193 4.59784 6.75836 5.83498 7.07336 7.03641C7.38836 8.23855 8.60336 8.94998 9.78622 8.62641C10.9684 8.30284 11.6726 7.06569 11.3569 5.86426ZM13.4076 9.78498L7.21836 11.4785L7.49479 12.5328L13.6841 10.8393L13.4076 9.78498Z"/>
</g>
<defs>
<filter id="@FilterId" x="0.856934" y="0.894775" width="17.5718" height="20.4166" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2137_72534"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_2137_72534" result="effect2_dropShadow_2137_72534"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_2137_72534" result="shape"/>
</filter>
</defs>
</svg>

@code
{
    public string FilterId { get; set; } = ComponentIdGenerator.Generate();
}
