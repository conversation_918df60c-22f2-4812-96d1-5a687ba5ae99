.giz-change-profile {

    &-dialog {
        color: #FAFAFA;
        width: 60.0rem;

        .giz-user-avatar {
            width: 13.1rem;
            height: 13.1rem;
        }

        .giz-input-control {
            .giz-input-label {
                @include font-s-theme-client($font-weight-light-theme-client);
            }
        }
    }

    &-section {
        &__header {
            margin-bottom: 1.6rem;
            color: rgba(255, 255, 255, 0.6);
            @include font-l-theme-client($font-weight-light-theme-client);
        }

        &__body {
        }
    }

    &-group {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.6rem;
        align-items: center;

        &__username {
            .giz-input-control {
                .giz-input-label {
                    @include font-m-theme-client($font-weight-light-theme-client);
                }

                .giz-input-validation-label {
                    display: none;
                }
            }

            &__tip {
                margin-top: 0.4rem;
                @include font-s-theme-client($font-weight-light-theme-client);
            }
        }

        &__avatar {
            justify-self: center;

            svg {
                color: rgba(250, 250, 250, 0.16);
            }
        }
    }
}
