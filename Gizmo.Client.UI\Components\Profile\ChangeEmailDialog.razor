﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase
@using Microsoft.AspNetCore.Components.Forms

<HostedDialog>
    <div class="giz-client-dialog giz-change-email-dialog">
        <div class="giz-client-dialog__header">
            <div class="giz-client-dialog__header__title">@LocalizationService.GetString("GIZ_CHANGE_EMAIL_TITLE")</div>
            <IconButton SVGIcon="Icons.Close" Variant="ButtonVariants.Text" Size="ButtonSizes.Small" Class="close-btn" @onclick="() => CloseDialog()" />
        </div>
        @switch (ViewState.PageIndex)
        {
            case 0:

                <div class="giz-client-dialog__body">
                    <div class="giz-change-email-dialog-message">@LocalizationService.GetString("GIZ_CHANGE_EMAIL_MESSAGE")</div>
                    <EditForm EditContext="@UserChangeEmailService.EditContext">
                        <div class="giz-form-input">
                            <TextInput IsTransparent="true"
                                       Value="@ViewState.Email"
                                       ValueChanged="@((string value) => UserChangeEmailService.SetEmail(value))"
                                       ValueExpression="@(() => ViewState.Email)"
                                       Size="InputSizes.Medium"
                                       IsFullWidth="true"
                               Label="@LocalizationService.GetString("GIZ_GEN_EMAIL")" />
                        </div>
                    </EditForm>
                </div>
                <div class="giz-client-dialog__footer">
                    <Button IsDisabled="@(ViewState.IsLoading)"
                            Color="ButtonColors.Accent"
                            Size="ButtonSizes.Large"
                            IsFullWidth="true"
                            @onclick="@(() => UserChangeEmailService.SendConfirmationCodeAsync())">
                        <ChildContent>
                            @if (ViewState.IsLoading)
                            {
                                <Spinner />
                            }
                            else
                            {
                                <div>@LocalizationService.GetString("GIZ_USER_CONFIRMATION_GET_CONFIRMATION_CODE")</div>
                            }
                        </ChildContent>
                    </Button>
                </div>

                break;

            case 1:

                <div class="giz-client-dialog__body">
                    <div class="giz-change-email-dialog-message">@LocalizationService.GetString("GIZ_CHANGE_EMAIL_SUCCESS_MESSAGE")</div>
                    <div class="giz-change-email-dialog-email">TODO: A...</div>
                    <EditForm EditContext="@UserChangeEmailService.EditContext">
                        <div class="giz-form-input">
                            <TextInput Value="@ViewState.ConfirmationCode"
                                       ValueChanged="@((string value) => UserChangeEmailService.SetConfirmationCode(value))"
                                       ValueExpression="@(() => ViewState.ConfirmationCode)"
                                       Size="InputSizes.Medium"
                                       IsFullWidth="true"
                                       Label="Enter the code" />
                        </div>
                    </EditForm>
                </div>
                <div class="giz-client-dialog__footer">
                    <Button Color="ButtonColors.Accent" Size="ButtonSizes.Large" IsFullWidth="true" @onclick="@(() => UserChangeEmailService.VerifyAsync())">Verify</Button>
                  
                    <div class="giz-resend-button">
                        <Button Variant="ButtonVariants.Outline"
                                Size="ButtonSizes.Large"
                                IsFullWidth="true"
                                IsDisabled="@(!ViewState.CanResend)"
                                Text="@LocalizationService.GetString("GIZ_GEN_RESEND")"
                                @onclick="@(() => UserChangeEmailService.ResendAsync())" />
                        <div class="giz-resend-button__time">@string.Format("{0:mm\\:ss}", ViewState.ResendTimeLeft)</div>
                    </div>
                </div>

                break;
                
            case 2:
                
                <div>Well done!</div>

                break;
        }
    </div>
</HostedDialog>
