﻿@namespace Gizmo.Client.UI.Pages
@inherits CustomDOMComponentBase
@layout _Layout_Login
@using System.Threading;
@using Gizmo.UI;
@using Microsoft.AspNetCore.Components.Forms

<LoginCard>
    <CardHeader>
        <div class="giz-login-links">
            <div class="giz-back-button" @onclick="@(() => NavigationService.NavigateTo(ClientRoutes.RegistrationConfirmationMethodRoute))">
                <Icon SVGIcon="Icons.ArrowLeft_Client" Size="IconSizes.ExtraSmall" />
            </div>
            <div class="giz-login-new-user">@LocalizationService.GetString("GIZ_LOGIN_ALREADY_A_MEMBER") <a href="@ClientRoutes.LoginRoute">@LocalizationService.GetString("GIZ_REGISTRATION_SIGN_IN")</a></div>
        </div>
        <div class="giz-login-alerts">
            @if (ViewState.HasError)
            {
                <Toast CanClose="true" AlertType="AlertTypes.Danger" Text="@ViewState.ErrorMessage" />
            }
        </div>
    </CardHeader>
    <CardBody>
        <EditForm EditContext="@UserRegistrationConfirmationService.EditContext">
            <div class="giz-login-title">@LocalizationService.GetString("GIZ_REGISTRATION_SIGN_UP_TITLE")</div>
            <div class="giz-login-subtitle">@ViewState.ConfirmationCodeMessage</div>
            <div class="giz-form-input">
                <TextInput UpdateOnInput="true"
                           Placeholder="123 456"
                           Value="@ViewState.ConfirmationCode"
                           ValueChanged="@((string value) => UserRegistrationConfirmationService.SetConfirmationCode(value))"
                           ValueExpression="@(() => ViewState.ConfirmationCode)"
                           IsDisabled="@ViewState.IsLoading"
                           Size="InputSizes.Medium"
                           IsFullWidth="true"
                           Label="@LocalizationService.GetString("GIZ_PASSWORD_RECOVERY_ENTER_YOUR_CODE")" />
            </div>
            <div class="giz-form-input">
                <Button IsDisabled="@(ViewState.IsValid == false || ViewState.IsLoading)"
                        Color="ButtonColors.Accent"
                        Size="ButtonSizes.ExtraLarge"
                        IsFullWidth="true"
                        @onclick="@UserRegistrationConfirmationService.Confirm">
                    <ChildContent>
                        @if (ViewState.IsLoading)
                        {
                            <Spinner />
                        }
                        else
                        {
                            <div>@LocalizationService.GetString("GIZ_GEN_CONFIRM")</div>
                        }
                    </ChildContent>
                </Button>
            </div>
            @if (UserVerificationFallbackViewState.IsSMSFallbackAvailable)
            {
                <div class="giz-resend-button">
                    <Button Variant="ButtonVariants.Outline"
                            Size="ButtonSizes.ExtraLarge"
                            IsFullWidth="true"
                            IsDisabled="@(ViewState.IsLoading || UserVerificationFallbackViewState.IsVerificationFallbackLocked)"
                            @onclick="@UserRegistrationConfirmationService.SMSFallbackAsync">
                        <div>@LocalizationService.GetString("GIZ_USER_CONFIRMATION_RETRY_WITH_SMS")</div>
                        @if (UserVerificationFallbackViewState.IsVerificationFallbackLocked)
                        {
                            <div class="giz-resend-button__time">@string.Format("{0:mm\\:ss}", UserVerificationFallbackViewState.Countdown)</div>
                        }
                    </Button>
                </div>
            }
        </EditForm>
    </CardBody>
    <CardFooter>
        <InputLanguageMenu />
    </CardFooter>
</LoginCard>
