﻿@inherits CustomDOMComponentBase

<div class="giz-home-apps-wrapper">
    <div class="giz-home-apps">
        <div class="giz-home-apps__header">
            <div class="giz-home-apps__header__quick-launch">
                <QuickLauncher />
            </div>
            <div class="giz-home-apps__header__ads">
                <NewsRotator />
            </div>
        </div>
        <div class="giz-home__body giz-ads-container giz-scrollbar--v">
            @if (AdvertisementsViewState.Advertisements.Count() > 0)
            {
                <div class="giz-home__body__ads">
                    <ExpansionPanel IsCollapsed="@AdvertisementsViewState.IsCollapsed" IsCollapsedChanged="@((value) => AdvertisementsViewStateService.SetCollapsed(value))">
                        <ExpansionPanelHeader>
                            @LocalizationService.GetString("GIZ_GEN_ADS")
                        </ExpansionPanelHeader>
                        <ChildContent>
                            <AdsCarousel Interval="5000" />
                        </ChildContent>
                    </ExpansionPanel>
                </div>
            }
            <div class="giz-home__body__popular">
                
                @if (ViewState.PopularProducts.Any())
                {
                    <div class="giz-section">
                        <div class="giz-section__header">
                            @LocalizationService.GetString("GIZ_GEN_POPULAR_PRODUCTS")
                        </div>
                        <div class="giz-section__body">
                            <ProductVirtualizedCards Items="ViewState.PopularProducts" ColumnsCount="PopularItemsOptions.Value.HomePageMaxItemsPerRow" />
                        </div>
                    </div>
                }

                @if (ViewState.PopularApplications.Any())
                {
                    <div class="giz-section">
                        <div class="giz-section__header">
                            @LocalizationService.GetString("GIZ_GEN_POPULAR_APPS")
                        </div>
                        <div class="giz-section__body">
                            <ApplicationVirtualizedCards Items="ViewState.PopularApplications" ColumnsCount="PopularItemsOptions.Value.HomePageMaxItemsPerRow" />
                        </div>
                    </div>
                }

            </div>
        </div>
    </div>
</div>
