﻿@namespace Gizmo.Client.UI.Shared
@inherits CustomDOMComponentBase
@using Gizmo.Client.UI.View.States
@using Microsoft.AspNetCore.Components.Forms
@using System.Globalization

<Select HandleSVGIcon="Icons.Select_Client"
        ValidationErrorStyle="ValidationErrorStyles.BorderOnly"
        TValue="CultureInfo"
        Value="ViewState.CurrentCulture"
        ValueChanged="@ValueChangedHandler"
        Size="InputSizes.Small"
        OpenDirection="PopupOpenDirections.Top"
        Class="giz-client-language-menu"
        PopupClass="giz-scrollbar--v">
        
    @foreach (var culture in ViewState.AvailableCultures)
    {
        <SelectItem Value="@culture">
            <div class="giz-input-language-menu-item">
                <div class="giz-input-language-menu-item__flag">
                    <Icon Size="IconSizes.Small" SVGIcon="Icons.Language_Client" />
                </div>
                <div>@culture.TwoLetterISOLanguageName</div>
            </div>
        </SelectItem>
    }

</Select>
