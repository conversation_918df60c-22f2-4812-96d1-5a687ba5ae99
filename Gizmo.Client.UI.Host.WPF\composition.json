{
  // UI Composition configuration
  // This file describes the skin file its dependencies and any additional configuration
  // This file should be published in the skin base folder and is required, developer have the responsibility of creating  it
  // We map this file to UICompositionOptions class mainly used by ui composition service
  "UIComposition": {
    "AppAssembly": "Gizmo.Client.UI.dll",
    "AdditionalAssemblies": [ "Gizmo.Web.Components.dll" ],
    "RootComponentType": "Gizmo.Client.UI.App,Gizmo.Client.UI",
    "NotificationsComponentType": "Gizmo.Client.UI.Components.NotificationsHost,Gizmo.Client.UI"
  }
}