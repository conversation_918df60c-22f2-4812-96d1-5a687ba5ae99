//=============== Executable Card ================//

.giz-executable-card {
    display: grid;
    grid-template-columns: min-content 1fr;
    grid-template-rows: min-content min-content;
    gap: 1.6rem;
    align-items: center;

    &__executable {
        &-image {
            grid-column-start: 1;
            grid-column-end: 2;
            grid-row-start: 2;
            grid-row-end: 3;
            position: relative;
            width: 4.8rem;
            height: 4.8rem;

            img {
                width: -moz-available;
                width: -webkit-fill-available;
                height: -moz-available;
                height: -webkit-fill-available;
            }
        }

        &-status {
            position: absolute;
            bottom: 0.4rem;
            left: 0.3rem;
            padding: 0.4rem;
            background-color: rgba(12, 19, 29, 0.8);
            border-radius: 1.9rem;
            color: #FFFFFF;
            text-shadow: 0 0.4rem 0.4rem rgba(0, 0, 0, 0.25);
            @include font-s-theme-client($font-weight-light-theme-client);

            &__bullet {
                display: inline-block;
                height: 0.8rem;
                width: 0.8rem;
                border-radius: 50%;
                margin-right: 0.2rem;
                background-color: #DC1C1C;
                box-shadow: 0 0 0.7rem 0.3rem rgba(245, 6, 6, 0.25);
            }
        }

        &-title {
            grid-column-start: 1;
            grid-column-end: 3;
            grid-row-start: 1;
            grid-row-end: 2;
            color: #FFFFFF;
            white-space: nowrap;
            @include font-m-theme-client($font-weight-regular-theme-client);
        }

        &-actions {
            grid-column-start: 2;
            grid-column-end: 3;
            grid-row-start: 2;
            grid-row-end: 3;
        }
    }
}
