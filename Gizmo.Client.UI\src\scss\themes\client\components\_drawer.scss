//=============== Drawer ================//

.giz-drawer {

    &-content {
        display: none;
        position: absolute;
        top: 0;
        right: -100%;
        z-index: 100;
        height: 100vh;
        background-color: #22272B;
        box-shadow: 0 0.4rem 0.4rem rgba(0, 0, 0, 0.08), 0 0.8rem 2.4rem rgba(0, 0, 0, 0.32);
        overflow: auto;
        transition: right 0.3s ease-in-out;
    }

    &--open {
        .giz-drawer-content {
            display: block;
            right: 0;
        }
    }

    & > .giz-overlay {
        background: linear-gradient(180deg, rgba(35, 35, 45, 0.9) 0%, rgba(42, 44, 53, 0.15) 100%);
    }
}
