﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase
@using Microsoft.AspNetCore.Components.Forms

<HostedDialog>
    <div class="giz-client-dialog giz-change-password-dialog">
        <div class="giz-client-dialog__header">
            <div class="giz-client-dialog__header__title">@LocalizationService.GetString("GIZ_GEN_PASSWORD")</div>
            @if (DisplayOptions.Closable)
            {
                <IconButton SVGIcon="Icons.Close" Variant="ButtonVariants.Text" Size="ButtonSizes.Small" Class="close-btn" @onclick="() => CloseDialog()" />
            }
        </div>

        @if (!ViewState.IsComplete)
        {
            <div class="giz-client-dialog__body">
                <EditForm EditContext="@UserChangePasswordService.EditContext">
                    @if (ViewState.ShowOldPassword)
                    {
                        <div class="giz-form-input">
                            <PasswordInput UpdateOnInput="true"
                                       ShowRevealButton="true"
                                       IsTransparent="true"
                                       Value="@ViewState.OldPassword"
                                       ValueChanged="@((string value) => UserChangePasswordService.SetOldPassword(value))"
                                       ValueExpression="@(() => ViewState.OldPassword)"
                                       Size="InputSizes.Small"
                                       IsFullWidth="true"
                                       Label="@LocalizationService.GetString("GIZ_CHANGE_PASSWORD_CURRENT_PASSWORD")" />
                        </div>
                    }
                    <div class="giz-form-input">
                        <PasswordInput UpdateOnInput="true"
                                       ShowRevealButton="true"
                                       IsTransparent="true"
                                       Value="@ViewState.NewPassword"
                                       ValueChanged="@((string value) => UserChangePasswordService.SetNewPassword(value))"
                                       ValueExpression="@(() => ViewState.NewPassword)"
                                       Size="InputSizes.Small"
                                       IsFullWidth="true"
                                       Label="@LocalizationService.GetString("GIZ_CHANGE_PASSWORD_NEW_PASSWORD")" />
                    </div>
                    <div class="giz-form-input">
                        <PasswordInput UpdateOnInput="true"
                                       ShowRevealButton="true"
                                       IsTransparent="true"
                                       Value="@ViewState.RepeatPassword"
                                       ValueChanged="@((string value) => UserChangePasswordService.SetRepeatPassword(value))"
                                       ValueExpression="@(() => ViewState.RepeatPassword)"
                                       Size="InputSizes.Small"
                                       IsFullWidth="true"
                                       Label="@LocalizationService.GetString("GIZ_CHANGE_PASSWORD_CONFIRM_PASSWORD")" />
                    </div>
                </EditForm>
            </div>
            <div class="giz-client-dialog__footer">
                <Button IsDisabled="@(ViewState.IsValid == false)"
                        Color="ButtonColors.Accent"
                        Size="ButtonSizes.Large"
                        IsFullWidth="true"
                        @onclick="@(() => UserChangePasswordService.SubmitAsync())"
                        Text="@LocalizationService.GetString("GIZ_USER_CHANGE_PASSWORD")" />
            </div>
        }
        else
        {
            @if (ViewState.HasError)
            {
                <div class="giz-client-alert-dialog">
                    <div class="giz-client-alert-dialog__icon giz-client-alert-dialog__icon--error">
                        <Icon Size="IconSizes.Large" SVGIcon="Icons.Close_Client" />
                    </div>
                    <div class="giz-client-alert-dialog__body giz-scrollbar--v">
                        <div class="giz-client-alert-message">@ViewState.ErrorMessage</div>
                    </div>
                    <div class="giz-client-alert-dialog__footer">
                        <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog())" Text="@LocalizationService.GetString("GIZ_GEN_CLOSE")" />
                    </div>
                </div>
            }
            else
            {
                <div class="giz-client-alert-dialog">
                    <div class="giz-client-alert-dialog__icon giz-client-alert-dialog__icon--success">
                        <Icon Size="IconSizes.Large" SVGIcon="Icons.Check" />
                    </div>
                    <div class="giz-client-alert-dialog__body">
                        @LocalizationService.GetString("GIZ_GEN_SUCCESS")
                    </div>
                    <div class="giz-client-alert-dialog__footer">
                        <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" @onclick="@(() => CloseDialog())" Text="@LocalizationService.GetString("GIZ_GEN_CLOSE")" />
                    </div>
                </div>
            }
        }

    </div>
</HostedDialog>
