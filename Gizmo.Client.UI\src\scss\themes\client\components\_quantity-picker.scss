.giz-shop-quantity-picker {
    display: grid;
    grid-template-columns: min-content 1fr min-content;
    align-items: center;
    border: 0.1rem solid #FFFFFF;
    border-radius: 0.8rem;
    text-align: center;
    color: #FFFFFF;
    padding: 0 0.6rem;
    @include font-l-theme-client($font-weight-regular-theme-client);

    button {
        background-color: initial;
        border: none;
        color: #FFFFFF;
        cursor: pointer;
        padding: 0 0.6rem;
        @include font-l-theme-client($font-weight-regular-theme-client);
    }

    &--small {
        height: 3.2rem;

        .giz-decrease-btn {
            .giz-button__content {
                @include font-l-theme-client($font-weight-regular-theme-client);
            }
        }

        .giz-increase-btn {
            .giz-button__content {
                @include font-l-theme-client($font-weight-regular-theme-client);
            }
        }
    }

    &--medium {
        height: 3.6rem;

        .giz-decrease-btn {
            .giz-button__content {
                @include font-xl-theme-client($font-weight-regular-theme-client);
            }
        }

        .giz-increase-btn {
            .giz-button__content {
                @include font-xl-theme-client($font-weight-regular-theme-client);
            }
        }
    }

    &--extra-large {
        height: 4.8rem;

        .giz-decrease-btn {
            .giz-button__content {
                @include font-xxl-theme-client($font-weight-regular-theme-client);
            }
        }

        .giz-increase-btn {
            .giz-button__content {
                @include font-xxl-theme-client($font-weight-regular-theme-client);
            }
        }
    }
}
