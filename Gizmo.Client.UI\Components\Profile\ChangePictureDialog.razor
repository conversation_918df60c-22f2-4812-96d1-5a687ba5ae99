﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase
@using Microsoft.AspNetCore.Components.Forms

<HostedDialog>
    <div class="giz-client-dialog giz-change-picture-dialog">
        <div class="giz-client-dialog__header">
            <div class="giz-client-dialog__header__title">Profile picture</div>
            <IconButton SVGIcon="Icons.Close" Variant="ButtonVariants.Text" Size="ButtonSizes.Small" Class="close-btn" @onclick="() => CloseDialog()" />
        </div>
        <div class="giz-client-dialog__body">
            <div class="giz-change-picture-dialog-image">
                <img src="@Image" />
                <Button Size="ButtonSizes.ExtraSmall" LeftSVGIcon="Icons.Trash2_Client" Class="giz-change-picture-dialog-image-delete-button" />
            </div>
            <div>
                <div class="giz-form-input">
                    <div class="giz-input-label">Upload Image</div>
                    <FileInput IsFullWidth="true" OnChange="@OnInputFileChange">
                        <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Small" IsFullWidth="true">Upload</Button>
                    </FileInput>
                </div>
                <div class="giz-form-input">
                    <TextInput IsTransparent="true" TValue="string" Size="InputSizes.Small" IsFullWidth="true" Label="Or enter an image link:" />
                </div>
                <div class="giz-change-picture-dialog-message">You can upload images in PNG or JPG formats. Image should be at least 200x200 pixels and no more than 7 MB.</div>
            </div>
        </div>
        <div class="giz-client-dialog__footer">
            <Button Variant="ButtonVariants.Outline" Size="ButtonSizes.Large" IsFullWidth="false">Cancel</Button>
            <Button Color="ButtonColors.Accent" Size="ButtonSizes.Large" IsFullWidth="false">Save</Button>
        </div>
    </div>
</HostedDialog>
