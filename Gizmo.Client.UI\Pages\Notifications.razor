﻿@using Gizmo.UI;
@inherits ComponentBase

<div client-theme="true" class="giz-notifications @(_slideOut ? "slide-out" : "slide-in")">
    <div class="giz-notifications__header">
        <div @onclick="DemoAdd">Notifications</div>
        <div>
            <IconButton SVGIcon="Icons.Close" Variant="ButtonVariants.Text" Size="ButtonSizes.Small" Class="close-btn" @onclick="() => CloseNotifications()" />
        </div>
    </div>
    <div class="giz-notifications__body giz-scrollbar--v">
        @for (int i = 0; i < _total; i++)
        {
            var local_i = i;
            <div class="giz-notification-wrapper @(_slideInIndex == local_i ? "slide-in" : _slideOutIndex == local_i ? "slide-out" : ""))">
                <GizNotification Icon="AlertTypes.Danger" OnClose="OnCloseHandler" />
            </div>
        }
    </div>
</div>
