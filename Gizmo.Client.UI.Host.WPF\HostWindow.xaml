﻿<Window x:Class="Gizmo.Client.UI.Host.WPF.HostWindow"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Gizmo.Client.UI.Host.WPF"
        xmlns:blazor="clr-namespace:Microsoft.AspNetCore.Components.WebView.Wpf;assembly=Microsoft.AspNetCore.Components.WebView.Wpf"      
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        WindowStyle="SingleBorderWindow"
        Title="MainWindow" Height="450" Width="800">
    <Grid>
        <blazor:BlazorWebView x:Name="_BLAZOR_WEB_VIEW" HostPage="wwwroot\index.html" Services="{StaticResource ResourceKey=services}">
            <blazor:BlazorWebView.RootComponents>
                <blazor:RootComponent x:Name="_ROOT_COMPONENT" Selector="#app"></blazor:RootComponent>
            </blazor:BlazorWebView.RootComponents>
        </blazor:BlazorWebView>
    </Grid>
</Window>
