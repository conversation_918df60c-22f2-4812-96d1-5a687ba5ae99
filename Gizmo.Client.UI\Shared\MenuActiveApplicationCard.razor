﻿@namespace Gizmo.Client.UI.Shared
@inherits CustomDOMComponentBase

<tr class="giz-active-app-card">
    <td class="giz-active-app-card__image">

        <GizImage ImageType="Gizmo.UI.ImageType.Executable" ImageId="@Executable.ImageId">
            <EmptyResultPlaceholder>
                <div class="giz-default-image">
                    <img src="_content/Gizmo.Client.UI/img/no-exe-image.svg" alt='loading' />
                </div>
            </EmptyResultPlaceholder>
        </GizImage>

    </td>
    <td class="giz-active-app-card__info">
        <div class="giz-active-app-card__info__category">Gaming</div>
        <div class="giz-active-app-card__info__title">@Executable.Caption</div>
    </td>
    <td class="giz-active-app-card__actions">
        <ExecutableLaunchButton @key="Executable.ExecutableId" ExecutableId="@Executable.ExecutableId" Size="ButtonSizes.Large" IsFullWidth="true" />
    </td>
</tr>
