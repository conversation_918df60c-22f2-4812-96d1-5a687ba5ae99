﻿@namespace Gizmo.Client.UI.Pages
@inherits CustomDOMComponentBase
@layout _Layout_Login
@using System.Threading;
@using Gizmo.UI;
@using Microsoft.AspNetCore.Components.Forms

<LoginCard>
    <CardHeader>
        <div class="giz-login-links">
            <div class="giz-back-button" @onclick="@(() => NavigationService.NavigateTo(ClientRoutes.LoginRoute))">
                <Icon SVGIcon="Icons.ArrowLeft_Client" Size="IconSizes.ExtraSmall" />
            </div>
            @if (UserRegisterConfigurationViewState.IsEnabled)
            {
                <div class="giz-login-new-user">@LocalizationService.GetString("GIZ_LOGIN_NOT_A_MEMBER") <a @onclick="UserLoginService.OpenRegistrationAsync">@LocalizationService.GetString("GIZ_LOGIN_SIGN_UP_NOW")</a></div>
            }
        </div>
        <div class="giz-login-alerts">
            @if (ViewState.HasError)
            {
                <Toast CanClose="true" AlertType="AlertTypes.Danger" Text="@ViewState.ErrorMessage" />
            }
        </div>
    </CardHeader>
    <CardBody>
        <EditForm EditContext="@UserPasswordRecoveryService.EditContext">
            <div class="giz-login-title">@LocalizationService.GetString("GIZ_PASSWORD_RECOVERY_FORGOT_PASSWORD")</div>
            @*<div>
                <ButtonGroup Class="giz-recovery-method" IsMandatory="true" SelectedItemsChanged="@SelectRecoveryMethod" IsDisabled="@ViewState.IsLoading">
                    <Button Name="Phone" IsSelected="@(ViewState.SelectedRecoveryMethod == UserRecoveryMethod.MobilePhone)">@LocalizationService.GetString("GIZ_GEN_PHONE_NUMBER")</Button>
                    <Button Name="Email" IsSelected="@(ViewState.SelectedRecoveryMethod == UserRecoveryMethod.Email)">@LocalizationService.GetString("GIZ_GEN_EMAIL")</Button>
                </ButtonGroup>
            </div>*@
            <div class="giz-login-subtitle">
                @if (ViewState.SelectedRecoveryMethod == UserRecoveryMethod.Email)
                {
                    @LocalizationService.GetString("GIZ_PASSWORD_RECOVERY_ENTER_EMAIL")
                }
                else if (ViewState.SelectedRecoveryMethod == UserRecoveryMethod.Mobile)
                {
                    @LocalizationService.GetString("GIZ_PASSWORD_RECOVERY_ENTER_PHONE_NUMBER")
                }
            </div>
            <div class="giz-form-input">
                @if (ViewState.SelectedRecoveryMethod == UserRecoveryMethod.Email)
                {
                    <TextInput UpdateOnInput="true"
                               Value="@ViewState.Email"
                               ValueChanged="@((string value) => UserPasswordRecoveryService.SetEmail(value))"
                               ValueExpression="@(() => ViewState.Email)"
                               IsDisabled="@ViewState.IsLoading"
                               Size="InputSizes.Medium"
                               IsFullWidth="true"
                               Label="@LocalizationService.GetString("GIZ_GEN_EMAIL_ADDRESS")"/>
                }
                else if (ViewState.SelectedRecoveryMethod == UserRecoveryMethod.Mobile)
                {
                    <MaskedPhoneInput Mask="###-###-####"
                                      AllowMoreDigits="true"
                                      Value="@ViewState.MobilePhone"
                                      ValueChanged="@((string value) => UserPasswordRecoveryService.SetMobilePhone(value))"
                                      ValueExpression="@(() => ViewState.MobilePhone)"
                                      IsDisabled="@ViewState.IsLoading"
                                      Size="InputSizes.Medium"
                                      IsFullWidth="true"
                                      Label="@LocalizationService.GetString("GIZ_GEN_PHONE_NUMBER")" />
                }
            </div>
            <div>
                <Button IsDisabled="@(ViewState.SelectedRecoveryMethod == UserRecoveryMethod.None || ViewState.IsValid == false || ViewState.IsLoading || UserVerificationViewState.IsVerificationLocked)"
                        Color="ButtonColors.Accent" Size="ButtonSizes.ExtraLarge"
                        IsFullWidth="true"
                        @onclick="@(() => UserPasswordRecoveryService.SubmitAsync(false))">
                    <ChildContent>
                        @if (ViewState.IsLoading)
                        {
                                <Spinner />
                        }
                        else
                        {
                            <div>@LocalizationService.GetString("GIZ_GEN_CONTINUE")</div>
                            @if (UserVerificationViewState.IsVerificationLocked)
                            {
                                <div class="giz-resend-button__time">@string.Format("{0:mm\\:ss}", UserVerificationViewState.Countdown)</div>
                            }
                        }
                    </ChildContent>
                </Button>
            </div>
        </EditForm>
    </CardBody>
    <CardFooter>
        <InputLanguageMenu />
    </CardFooter>
</LoginCard>
