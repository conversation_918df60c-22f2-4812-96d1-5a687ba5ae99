//=============== Theme Client Variables ================//

$client-theme-comp-attr: 'client-theme';

//==Colors
$primary-color-theme-client: #3F8CFF;

$bg-alert-theme-client: #F73B3B;

$typo-primary-theme-client: #FAFAFA;
$typo-secondary-theme-client: rgba(255, 255, 255, 0.6);
$typo-brand-theme-client: #0091E6;
$typo-ghost-theme-client: rgba(255, 255, 255, 0.3);
$typo-link-theme-client: #0F9FFF;
$typo-link-hover-theme-client: #6FA5C8;
$typo-link-mirror-theme-client: #57BCFF;
$typo-system-theme-client: #636E83;
$typo-normal-theme-client: #009BF5;
$typo-success-theme-client: #10AE79;
$typo-warning-theme-client: #E68200;
$typo-alert-theme-client: #F73B3B;
$typo-caution-theme-client: #F8C735;
$typo-critical-theme-client: #62001D;

//==Shadows
$elevation-0-theme-client: none;
$elevation-1-theme-client: 0px 1px 12px rgba(37, 140, 246, 0.5);
$elevation-2-theme-client: 0px 5px 10px rgba(8, 8, 13, 0.3);
$elevation-3-theme-client: 0px 4px 8px rgba(4, 5, 6, 0.4);
$elevation-4-theme-client: 0px 5px 10px rgba(8, 8, 13, 0.3);
$elevation-5-theme-client: 0px 20px 15px 5px rgba(4, 5, 6, 0.4);

//Old
$unknown-color: orange;

//z-indexes

$notifications-index: 50;
$header-dropdown-index: 100;
$global-search-index: 100;
$lock-overlay-index: 1000;