﻿@namespace Gizmo.Client.UI.Pages
@inherits CustomDOMComponentBase

<div class="giz-home-apps-wrapper">
    <div class="giz-home-apps">
        <div class="giz-home-apps__header">
            <div class="giz-home-apps__header__quick-launch">
                <QuickLauncher />
            </div>
            <div class="giz-home-apps__header__ads">
                <NewsRotator />
            </div>
        </div>
        <div class="giz-apps__body">
            <div class="giz-section giz-section--stuck">
                <div class="giz-section__header">
                    <AppFilters />
                </div>
            </div>
            <div class="giz-apps__body__content giz-ads-container giz-scrollbar--v">
                @if (AdvertisementsViewState.Advertisements.Count() > 0)
                {
                    <div class="giz-apps__body__content__ads">
                        <ExpansionPanel IsCollapsed="@AdvertisementsViewState.IsCollapsed" IsCollapsedChanged="@((value) => AdvertisementsViewStateService.SetCollapsed(value))">
                            <ExpansionPanelHeader>
                                @LocalizationService.GetString("GIZ_GEN_ADS")
                            </ExpansionPanelHeader>
                            <ChildContent>
                                <AdsCarousel Interval="5000" />
                            </ChildContent>
                        </ExpansionPanel>
                    </div>
                }
                <div class="giz-apps__body__content__popular">
                    <div class="giz-section">
                        <div class="giz-section__header">
                            <AppFilters />
                        </div>
                        <div class="giz-section__body">
                            @if (ViewState.Applications.Any())
                            {
                                <ApplicationVirtualizedCards Items="ViewState.Applications" ColumnsCount="PopularItemsOptions.Value.AppsPageMaxItemsPerRow" />
                            }
                            else
                            {
                                <div class="global-search-no-results">
                                    <div class="global-search-no-results__header">@LocalizationService.GetString("GIZ_GLOBAL_SEARCH_NO_MATCH_FOUND_FOR") <span class="global-search-pattern">@ViewState.SearchPattern</span></div>
                                    <div class="global-search-no-results__body">@LocalizationService.GetString("GIZ_GLOBAL_SEARCH_NO_MATCH_FOUND_MESSAGE")</div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
