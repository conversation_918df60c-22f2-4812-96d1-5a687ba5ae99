//== header section common style == //

.giz-header {
  display: flex;
  align-items: stretch;
  height: 100%;
  padding: 0 1.6rem;

  .giz-user-avatar {
      width: 4rem;
      height: 4rem;
  }

  &__logo {
    display: flex;
    align-items: center;
    padding: 0 3.2rem 0 0;
  }

  &__modules-menu {
    display: flex;
    gap: 1.6rem;
    padding: 0 3.2rem 0 0;

    &-item {
      width: 5.6rem;

      & > a {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: white;

        .giz-icon--large {
          width: 3rem;
          height: 3rem;
          color: #fafafa;
        }

        &.active {
          color: $primary-color-theme-client;
          position: relative;

          .giz-icon--large {
            color: #0078d2;
          }

          &::before {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 0.3rem;
            background-color: $primary-color-theme-client;
            border-radius: 0.2rem;
          }
        }

        & > span {
          display: none;
        }
      }
    }
  }

  &__global-search {
    display: flex;
    align-items: center;
    flex-grow: 1;
    padding: 0 3.2rem 0 0;
  }

  &__user-menu {
    display: flex;
    align-items: stretch;
    justify-content: center;
    height: 100%;
    gap: 1.6rem;
    position: relative;

    &-item {
      display: flex;
      align-items: center;
      white-space: nowrap;

      @include font-xl-theme-client($font-weight-regular-theme-client);

      &__icon {
        display: flex;
        margin-right: 1rem;

        .giz-icon--small {
          width: 2rem;
          height: 2rem;
        }
      }

      & > .giz-user-menu-button {
        display: grid;
        grid-template-columns: auto auto auto;
        align-items: center;
        gap: 1rem;
        white-space: nowrap;
        padding: 0 0.7rem;
        border-radius: 0.8rem;
        cursor: pointer;
        height: 4.8rem;
        //Button
        background-color: transparent;
        border: none;
        color: #fafafa;
        @include font-xl-theme-client($font-weight-regular-theme-client);

        .giz-icon--extra-large {
          width: 4rem;
          height: 4rem;
        }
      }
    }
  }
}

.giz-user-dropdown {
  white-space: nowrap;

  .giz-dropdown-menu {
    min-width: 36rem;
  }

  &.open {
    .giz-user-menu-button {
      background-color: #1d334e;
      box-shadow: inset 0 0 10.1rem rgba(255, 255, 255, 0.06);

      .giz-icon {
        color: #0f7cfe;
      }
    }
  }
}

.user-menu-item-button {
  display: flex;
  cursor: pointer;

  &--box {
    width: 4.8rem;
    height: 4.8rem;
    background-color: rgba(250, 250, 250, 0.16);
    border-radius: 0.8rem;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.75);
    //Button
    border: none;
  }
}

.giz-user-online-deposit-dropdown {
    &.open {
        .user-menu-item-button--box {
            background-color: #1d334e;
            color: #0f7cfe;
        }
    }
}

.giz-notifications-dropdown {
  &.open {
    .user-menu-item-button--box {
      background-color: #1d334e;
      color: #0f7cfe;
    }
  }
}

.giz-active-apps-dropdown {
  &.open {
    .user-menu-item-button--box {
      background-color: #1d334e;
      color: #0f7cfe;
    }
  }
}
