﻿<Window x:Class="Gizmo.Client.UI.Host.WPF.NotificationsHost"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:blazor="clr-namespace:Microsoft.AspNetCore.Components.WebView.Wpf;assembly=Microsoft.AspNetCore.Components.WebView.Wpf" 
        xmlns:local="clr-namespace:Gizmo.Client.UI.Host.WPF"
        mc:Ignorable="d" d:DesignHeight="808.635" d:DesignWidth="797.5">
    <Border x:Name="_VIEW_HOST"  Width="600" Opacity="0.01" MinHeight="400" MaxHeight="600" BorderThickness="1,0,1,1" BorderBrush="Gray"   Background="Black" VerticalAlignment="Top" HorizontalAlignment="Center">
        
    </Border>
</Window>
