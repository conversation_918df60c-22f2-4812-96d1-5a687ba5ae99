//=============== ButtonGroup ================//

.giz-button-group {
    [#{$client-theme-comp-attr}] & {
        border: 0.1rem solid rgba(246, 251, 253, 0.28);
        border-radius: 0.8rem;
        padding: unset;

        .giz-button {
            text-transform: uppercase;
            color: white;
            background-color: unset;
            border-radius: unset;
            letter-spacing: normal;

            &:active {
                box-shadow: unset;
            }

            &:last-child {
                border-right: none;
            }

            &.selected {
                //background: linear-gradient(180deg, rgba(44, 124, 244, 0.25) 0%, rgba(13, 16, 23, 0) 100%);
            }
        }
    }
}