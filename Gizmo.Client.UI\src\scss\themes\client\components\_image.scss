.giz-image__spinner {
  padding: 10%;
  display: inline-block;
}

.giz-default-image {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.giz-image {
  &--fill {
    width: 100%;
    height: 100%;
    object-fit: fill;
  }

  &--contain {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  &--cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &--none {
    width: 100%;
    height: 100%;
    object-fit: none;
  }

  &--scale-down {
    width: 100%;
    height: 100%;
    object-fit: scale-down;
  }

  &-loading {
    &--wrapper {
      position: relative;
      height: 100%;
      width: 100%;
      background-color: #161a1d;
      z-index: 44;
      overflow: hidden;
      border-radius: 5px;
    }
    &--activity {
      position: absolute;
      left: -45%;
      height: 100%;
      width: 45%;
      background-image: linear-gradient(
        to left,
        rgba(251, 251, 251, 0.05),
        rgba(251, 251, 251, 0.3),
        rgba(251, 251, 251, 0.6),
        rgba(251, 251, 251, 0.3),
        rgba(251, 251, 251, 0.05)
      );
      background-image: -moz-linear-gradient(
        to left,
        rgba(251, 251, 251, 0.05),
        rgba(251, 251, 251, 0.3),
        rgba(251, 251, 251, 0.6),
        rgba(251, 251, 251, 0.3),
        rgba(251, 251, 251, 0.05)
      );
      background-image: -webkit-linear-gradient(
        to left,
        rgba(251, 251, 251, 0.05),
        rgba(251, 251, 251, 0.3),
        rgba(251, 251, 251, 0.6),
        rgba(251, 251, 251, 0.3),
        rgba(251, 251, 251, 0.05)
      );
      animation: gradientAnimation 1s infinite;
      z-index: 45;
    }
  }
}

@keyframes gradientAnimation {
  0% {
    left: -45%;
  }
  100% {
    left: 100%;
  }
}
