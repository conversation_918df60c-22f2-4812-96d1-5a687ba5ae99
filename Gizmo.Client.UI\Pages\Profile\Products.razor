﻿@namespace Gizmo.Client.UI.Pages
@inherits CustomDOMComponentBase
@using Gizmo.Client.UI.View.States

<div class="giz-profile">
    <div class="giz-profile__body">
        <ProfileHeader />
    
        <ProfileNavigation />

        <div class="giz-profile-user-purchases">
            <div class="giz-profile-user-purchases__header">
                Available time
            </div>

            <div class="giz-table-history">
                <DataGrid ItemSource="@ViewState.TimeProducts.ToList()"
                          Context="timeProducts"
                          HasStickyHeader="true"
                          Class="giz-scrollbar--v">
                    <ChildContent>
                        <DataGridColumn Field="@(nameof(TimeProductViewState.Title))" TItemType="TimeProductViewState" Context="timeProduct">
                            <HeaderTemplate>
                                Title
                            </HeaderTemplate>
                            <CellTemplate>
                                @timeProduct.Title
                            </CellTemplate>
                        </DataGridColumn>
                        <DataGridColumn Field="@(nameof(TimeProductViewState.Time))" TItemType="TimeProductViewState" Context="timeProduct">
                            <HeaderTemplate>
                                Time
                            </HeaderTemplate>
                            <CellTemplate>
                                @timeProduct.Time
                            </CellTemplate>
                        </DataGridColumn>
                        <DataGridColumn Field="@(nameof(TimeProductViewState.Title))" TItemType="TimeProductViewState" Context="timeProduct">
                            <HeaderTemplate>
                                Life time
                            </HeaderTemplate>
                            <CellTemplate>
                                @timeProduct.Title
                            </CellTemplate>
                        </DataGridColumn>
                        <DataGridColumn Field="@(nameof(TimeProductViewState.PurchaseDate))" TItemType="TimeProductViewState" Context="timeProduct">
                            <HeaderTemplate>
                                Purchase date
                            </HeaderTemplate>
                            <CellTemplate>
                                @timeProduct.PurchaseDate.ToString()
                            </CellTemplate>
                        </DataGridColumn>
                        <DataGridColumn Field="@(nameof(TimeProductViewState.Title))" TItemType="TimeProductViewState" Context="timeProduct">
                            <HeaderTemplate>
                                Receipt
                            </HeaderTemplate>
                            <CellTemplate>
                                @timeProduct.Title
                            </CellTemplate>
                        </DataGridColumn>
                    </ChildContent>
                </DataGrid>
            </div>
        </div>

    </div>
</div>
