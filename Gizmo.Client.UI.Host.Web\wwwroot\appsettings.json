﻿{
  "UIComposition": {
    "ApiEndpoint": "http://localhost",
    "RealTimeEndpoint": "http://localhost/rt",
    "AppAssembly": "Gizmo.Client.UI.dll",
    "AdditionalAssemblies": [ "Gizmo.Web.Components.dll" ],
    "RootComponentType": "Gizmo.Client.UI.App,Gizmo.Client.UI",
    "NotificationsComponentType": "Gizmo.Client.UI.Components.NotificationsHost,Gizmo.Client.UI"
  },
  "Interface": {
    "Background": "wallpaper.jpg",
    "Skin": "override_skin.css"
  },
  //https://learn.microsoft.com/en-us/dotnet/api/system.globalization.numberformatinfo?view=net-6.0
  "CurrencyOptions": {
    "CurrencySymbol": "€",
    "CurrencyDecimalDigits": 2,
    "CurrencyDecimalSeparator": ".",
    "CurrencyGroupSeparator": ",",
    "CurrencyGroupSizes": [ 3 ],
    "CurrencyNegativePattern": 0,
    "CurrencyPositivePattern": 0
  },
  "UserOnlineDepositOptions": {
    "ShowUserOnlineDeposit": true,
    "MaximumAmount": 100
  },
  "PopularItemsOptions": {
    "MaxPopularProducts": 16,
    "MaxPopularApplications": 16,
    "MaxQuickLaunchExecutables": 10,
    "HomePageMaxItemsPerRow": 8,
    "AppsPageMaxItemsPerRow": 8,
    "ProductsPageMaxItemsPerRow": 6
  },
  "HostQRCodeOptions": {
    "Enabled": true
  },
  "UserLoginOptions": {
    "Enabled": true
  }
}
