﻿@namespace Gizmo.Client.UI.Components

<svg width="138" height="246" viewBox="0 0 138 246" fill="none" xmlns="http://www.w3.org/2000/svg" class="giz-polygon-icon">
<g clip-path="url(#@Clip0rId)">
<g filter="url(#@FilterId)">
<path d="M77.6532 61.6551L118.983 85.5167C124.028 88.4295 127.136 93.8127 127.136 99.6384V147.362C127.136 153.187 124.028 158.57 118.983 161.483L77.6532 185.345C72.608 188.258 66.392 188.258 61.3468 185.345L20.0173 161.483C14.9721 158.57 11.8641 153.187 11.8641 147.362V99.6384C11.8641 93.8127 14.9721 88.4295 20.0173 85.5167L61.3468 61.6551C66.392 58.7422 72.608 58.7422 77.6532 61.6551Z" stroke="#A258FE" stroke-width="3.37373" shape-rendering="crispEdges"/>
</g>
</g>
<g clip-path="url(#@Clip1Id)">
<path d="M61.3468 61.6551C66.392 58.7422 72.608 58.7422 77.6532 61.6551L118.983 85.5167C124.028 88.4295 127.136 93.8127 127.136 99.6384V147.362C127.136 153.187 124.028 158.57 118.983 161.483L77.6532 185.345C72.608 188.258 66.392 188.258 61.3468 185.345L20.0173 161.483C14.9721 158.57 11.8641 153.187 11.8641 147.362V99.6384C11.8641 93.8127 14.9721 88.4295 20.0173 85.5167L61.3468 61.6551Z" stroke="#F2F2F2" stroke-width="3.37373"/>
</g>
<path d="M43.636 121.92C43.636 122.573 43.496 123.14 43.216 123.62C42.9493 124.1 42.5827 124.493 42.116 124.8C41.6627 125.107 41.1427 125.333 40.556 125.48V125.54C41.7027 125.673 42.5693 126.02 43.156 126.58C43.756 127.14 44.056 127.893 44.056 128.84C44.056 129.667 43.8493 130.413 43.436 131.08C43.036 131.733 42.4093 132.253 41.556 132.64C40.716 133.013 39.6293 133.2 38.296 133.2C37.5093 133.2 36.776 133.133 36.096 133C35.416 132.867 34.776 132.673 34.176 132.42V129.86C34.7893 130.167 35.4293 130.4 36.096 130.56C36.776 130.72 37.4027 130.8 37.976 130.8C39.056 130.8 39.8093 130.613 40.236 130.24C40.676 129.867 40.896 129.34 40.896 128.66C40.896 128.26 40.796 127.927 40.596 127.66C40.396 127.38 40.0427 127.173 39.536 127.04C39.0427 126.893 38.3493 126.82 37.456 126.82H36.376V124.5H37.476C38.356 124.5 39.0227 124.42 39.476 124.26C39.9427 124.087 40.256 123.86 40.416 123.58C40.5893 123.287 40.676 122.953 40.676 122.58C40.676 122.073 40.516 121.68 40.196 121.4C39.8893 121.107 39.3693 120.96 38.636 120.96C38.1827 120.96 37.7693 121.02 37.396 121.14C37.0227 121.247 36.6827 121.38 36.376 121.54C36.0827 121.7 35.8227 121.853 35.596 122L34.196 119.92C34.5693 119.653 34.9827 119.413 35.436 119.2C35.9027 118.987 36.416 118.82 36.976 118.7C37.536 118.58 38.1627 118.52 38.856 118.52C40.3227 118.52 41.4827 118.82 42.336 119.42C43.2027 120.007 43.636 120.84 43.636 121.92ZM55.5613 125.86C55.5613 127.007 55.468 128.033 55.2813 128.94C55.108 129.847 54.8213 130.62 54.4213 131.26C54.0347 131.887 53.5213 132.367 52.8813 132.7C52.2547 133.033 51.4813 133.2 50.5613 133.2C49.4147 133.2 48.4747 132.907 47.7413 132.32C47.008 131.733 46.4613 130.893 46.1013 129.8C45.7547 128.707 45.5813 127.393 45.5813 125.86C45.5813 124.313 45.7413 122.993 46.0613 121.9C46.3813 120.807 46.908 119.967 47.6413 119.38C48.3747 118.793 49.348 118.5 50.5613 118.5C51.708 118.5 52.648 118.793 53.3813 119.38C54.1147 119.953 54.6613 120.793 55.0213 121.9C55.3813 122.993 55.5613 124.313 55.5613 125.86ZM48.5813 125.86C48.5813 126.94 48.6413 127.847 48.7613 128.58C48.8813 129.3 49.0813 129.847 49.3613 130.22C49.6547 130.58 50.0547 130.76 50.5613 130.76C51.068 130.76 51.4613 130.58 51.7413 130.22C52.0347 129.86 52.2413 129.32 52.3613 128.6C52.4947 127.867 52.5613 126.953 52.5613 125.86C52.5613 124.767 52.4947 123.86 52.3613 123.14C52.2413 122.407 52.0347 121.86 51.7413 121.5C51.4613 121.127 51.068 120.94 50.5613 120.94C50.0547 120.94 49.6547 121.127 49.3613 121.5C49.0813 121.86 48.8813 122.407 48.7613 123.14C48.6413 123.86 48.5813 124.767 48.5813 125.86ZM81.0773 121.88C82.3173 121.88 83.2506 122.2 83.8773 122.84C84.5173 123.467 84.8373 124.48 84.8373 125.88V133H81.8573V126.62C81.8573 125.833 81.7239 125.247 81.4573 124.86C81.1906 124.46 80.7773 124.26 80.2173 124.26C79.4306 124.26 78.8706 124.54 78.5373 125.1C78.2039 125.66 78.0373 126.467 78.0373 127.52V133H75.0573V126.62C75.0573 126.1 74.9973 125.667 74.8773 125.32C74.7573 124.973 74.5773 124.713 74.3373 124.54C74.0973 124.353 73.7906 124.26 73.4173 124.26C72.8706 124.26 72.4373 124.4 72.1173 124.68C71.7973 124.96 71.5706 125.367 71.4373 125.9C71.3039 126.433 71.2373 127.087 71.2373 127.86V133H68.2573V122.08H70.5373L70.9373 123.48H71.0973C71.3239 123.107 71.6039 122.807 71.9373 122.58C72.2706 122.34 72.6373 122.167 73.0373 122.06C73.4506 121.94 73.8639 121.88 74.2773 121.88C75.0773 121.88 75.7573 122.013 76.3173 122.28C76.8773 122.533 77.3039 122.933 77.5973 123.48H77.8573C78.1906 122.92 78.6506 122.513 79.2373 122.26C79.8373 122.007 80.4506 121.88 81.0773 121.88ZM90.8857 122.08V133H87.9057V122.08H90.8857ZM89.4057 117.8C89.8457 117.8 90.2257 117.907 90.5457 118.12C90.8657 118.32 91.0257 118.7 91.0257 119.26C91.0257 119.807 90.8657 120.187 90.5457 120.4C90.2257 120.613 89.8457 120.72 89.4057 120.72C88.9524 120.72 88.5657 120.613 88.2457 120.4C87.939 120.187 87.7857 119.807 87.7857 119.26C87.7857 118.7 87.939 118.32 88.2457 118.12C88.5657 117.907 88.9524 117.8 89.4057 117.8ZM100.199 121.88C101.373 121.88 102.313 122.2 103.019 122.84C103.726 123.467 104.079 124.48 104.079 125.88V133H101.099V126.62C101.099 125.833 100.959 125.247 100.679 124.86C100.399 124.46 99.9528 124.26 99.3395 124.26C98.4328 124.26 97.8128 124.573 97.4795 125.2C97.1461 125.813 96.9795 126.7 96.9795 127.86V133H93.9995V122.08H96.2795L96.6795 123.48H96.8395C97.0795 123.107 97.3728 122.807 97.7195 122.58C98.0661 122.34 98.4528 122.167 98.8795 122.06C99.3061 121.94 99.7461 121.88 100.199 121.88Z" fill="#FAFAFA"/>
<defs>
<filter id="@FilterId" x="5.67894" y="57.7836" width="127.642" height="140.429" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.49831"/>
<feGaussianBlur stdDeviation="2.24915"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.635294 0 0 0 0 0.345098 0 0 0 0 0.996078 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2437_9884"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2437_9884" result="shape"/>
</filter>
<clipPath id="@Clip0rId">
<rect width="69" height="208" fill="white" transform="translate(69 19)"/>
</clipPath>
<clipPath id="@Clip1Id">
<rect width="69" height="208" fill="white" transform="translate(0 19)"/>
</clipPath>
</defs>
</svg>

@code
{
    public string FilterId { get; set; } = ComponentIdGenerator.Generate();
    public string Clip0rId { get; set; } = ComponentIdGenerator.Generate();
    public string Clip1Id { get; set; } = ComponentIdGenerator.Generate();
}
