﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase
@using System.Globalization

@if (Product.UnitPrice == 0 && (Product.UnitPointsPrice ?? 0) == 0)
{
    <span>@LocalizationService.GetString("GIZ_PRODUCT_PRICE_FREE")</span>
}
else
{
    @if (Product.UnitPrice > 0)
    {
        <span>@Product.UnitPrice.ToString("C", CultureInfo.CurrentCulture)</span>
    }

    @if (Product.UnitPrice > 0 && Product.UnitPointsPrice > 0)
    {
        @if (Product.PurchaseOptions.HasFlag(PurchaseOptionType.Or))
        {
            <span>@(" " + LocalizationService.GetString("GIZ_PRODUCT_PRICE_PURCHASE_OPTION_OR") + " ")</span>
        }
        else
        {
            <span>@(" " + LocalizationService.GetString("GIZ_PRODUCT_PRICE_PURCHASE_OPTION_AND") + " ")</span>
        }
    }

    @if (Product.UnitPointsPrice > 0)
    {
        <span>@Product.UnitPointsPrice</span>
        <Points2Icon />
    }
}
