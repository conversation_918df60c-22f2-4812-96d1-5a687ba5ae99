﻿@namespace Gizmo.Client.UI.Components
@inherits CustomDOMComponentBase

<div class="giz-carousel__navigation mb-4">
    <div class="giz-carousel__navigation__title">@Title</div>
    <div class="giz-carousel__navigation__buttons">
        <IconButton Variant="ButtonVariants.Text" Size="ButtonSizes.Small" SVGIcon="Icons.ArrowLeft_Client" @onclick="@OnClickPreviousButtonHandler" />
        <IconButton Variant="ButtonVariants.Text" Size="ButtonSizes.Small" SVGIcon="Icons.ArrowRight_Client" @onclick="@OnClickNextButtonHandler" />
    </div>
</div>
